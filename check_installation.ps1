Write-Host "Checking SOTFmenu installation..." -ForegroundColor Green
Write-Host ""

$documentsPath = [Environment]::GetFolderPath('MyDocuments')
$sotfPath = Join-Path $documentsPath "SOTF"
$configPath = Join-Path $sotfPath "config.ini"

Write-Host "Documents path: $documentsPath" -ForegroundColor Yellow
Write-Host "SOTF folder path: $sotfPath" -ForegroundColor Yellow

if (Test-Path $sotfPath) {
    Write-Host "✓ SOTF folder exists" -ForegroundColor Green
    
    if (Test-Path $configPath) {
        Write-Host "✓ Configuration file exists" -ForegroundColor Green
        Write-Host ""
        Write-Host "Configuration contents:" -ForegroundColor Cyan
        Get-Content $configPath | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
    } else {
        Write-Host "✗ Configuration file missing" -ForegroundColor Red
    }
} else {
    Write-Host "✗ SOTF folder does not exist" -ForegroundColor Red
}

Write-Host ""
Write-Host "Game directory files:" -ForegroundColor Cyan
$gameFiles = @("SonsOfTheForest.exe", "SOTFmenu.dll", "install_sotf_menu.bat", "launch_with_mod.bat")
foreach ($file in $gameFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file" -ForegroundColor Green
    } else {
        Write-Host "✗ $file" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Installation status complete." -ForegroundColor Green
