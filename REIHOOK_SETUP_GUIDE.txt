========================================
  REIHOOK MOD SETUP GUIDE
========================================

🎯 REIHOOK IS BETTER THAN SOTFMENU!
✅ More stable injection method
✅ Uses BepInEx framework (industry standard)
✅ Menu Key: F4 (much more reliable)
✅ Same features + more

========================================
STEP 1: INSTALL BEPINEX FRAMEWORK
========================================

BepInEx was already opened in your browser. Here's what to do:

1. 📥 DOWNLOAD BEPINEX:
   • Go to: https://github.com/BepInEx/BepInEx/releases
   • Look for the LATEST release (6.0.0+)
   • Download: "BepInEx_UnityIL2CPP_x64_6.0.0-be.XXX.zip"
   • (The one that says "UnityIL2CPP_x64" - this is important!)

2. 📂 EXTRACT TO GAME DIRECTORY:
   • Extract the ZIP file
   • Copy ALL contents to your game folder:
     C:\Program Files (x86)\Steam\steamapps\common\Sons Of The Forest\
   • You should see these new folders/files:
     - BepInEx\ (folder)
     - winhttp.dll (file)
     - doorstop_config.ini (file)

3. 🎮 INITIALIZE BEPINEX:
   • Launch Sons of the Forest once
   • Let it load completely to the main menu
   • Close the game
   • This creates the plugins folder: BepInEx\plugins\

========================================
STEP 2: INSTALL REIHOOK MOD
========================================

After BepInEx is installed and initialized:

1. 📁 LOCATE REIHOOK FILE:
   • You have: "ReiHook for Sons of the Forest v2.0_[unknowncheats.me]_.dll"
   • This is in your game directory

2. 📋 COPY TO PLUGINS:
   • Copy the ReiHook DLL file
   • Paste it into: BepInEx\plugins\
   • Rename it to: "ReiHook.dll" (simpler name)

3. ✅ VERIFY INSTALLATION:
   • Check that you have: BepInEx\plugins\ReiHook.dll

========================================
STEP 3: LAUNCH AND USE
========================================

🚀 LAUNCHING:
• Start Sons of the Forest normally (Steam or directly)
• BepInEx will automatically load ReiHook

🎮 USING THE MOD:
• Wait for game to fully load
• Start or load a game
• Press F4 to open ReiHook menu (NOT P!)
• Navigate with mouse
• Enable features as desired

========================================
REIHOOK FEATURES
========================================

🔑 MENU KEY: F4

👤 PLAYER:
• Transform [LeftControl] - teleport/noclip mode
• Unlimited Health
• Unlimited Stamina  
• Max Strength
• No Hunger, Thirst, Tiredness, Cold
• Give All Items

🎯 VISUALS & ESP:
• Enemy [Box, Name, Health, Distance]
• Animal [Name, Health, Distance]
• Hostile Animals [Name, Health, Distance]
• NPC [Name, Health, Distance]
• Vehicle [Name, Distance]
• Container [Name, Distance]
• Cave & Bunker [Name, Distance]
• Cannibal Village [Name, Count, Distance]

📡 2D RADAR:
• Shows Enemies, Animals, NPCs
• Configurable draw distance

⚔️ WEAPON:
• Unlimited Ammo
• Rapid Fire
• Infinite Rope Length

🛠️ OTHER MENUS:
• Item Spawner
• Mutant Spawner
• Animal Spawner
• World Editor
• Teleport to Cave/Bunker

========================================
AUTOMATED INSTALLATION
========================================

I've created helper scripts for you:

• INSTALL_BEPINEX.bat - Installs BepInEx framework
• INSTALL_REIHOOK.bat - Installs ReiHook mod
• LAUNCH_REIHOOK.bat - Launches game with mod

Run these in order after downloading BepInEx manually.

========================================
TROUBLESHOOTING
========================================

❌ F4 NOT WORKING:
• Make sure BepInEx initialized (run game once first)
• Check BepInEx\plugins\ReiHook.dll exists
• Set game to Borderless Window mode
• Close overlay programs

❌ BEPINEX NOT LOADING:
• Make sure you downloaded IL2CPP x64 version
• Check winhttp.dll is in game root directory
• Run game as administrator

❌ MOD NOT LOADING:
• Check BepInEx\LogOutput.log for errors
• Make sure ReiHook.dll is in plugins folder
• Verify game version compatibility

========================================
ADVANTAGES OVER SOTFMENU
========================================

✅ More reliable injection (BepInEx framework)
✅ F4 key works better than P key
✅ Better error logging and debugging
✅ More stable across game updates
✅ Industry-standard modding framework
✅ Less likely to be blocked by antivirus

========================================
NEXT STEPS
========================================

1. Download BepInEx from the opened GitHub page
2. Extract to game directory
3. Run game once to initialize
4. Run INSTALL_REIHOOK.bat to install the mod
5. Use LAUNCH_REIHOOK.bat to play with mods
6. Press F4 in-game to access the menu

🎉 ENJOY YOUR ENHANCED SONS OF THE FOREST EXPERIENCE!
