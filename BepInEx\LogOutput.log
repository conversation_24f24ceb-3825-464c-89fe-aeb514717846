[Message: Preloader] BepInEx 6.0.0-be.697 - SonsOfTheForest
[Message: Preloader] Built from commit 53625800b86f6c68751445248260edf0b27a71c2
[Info   :   BepInEx] System platform: Windows 10 64-bit
[Info   :   BepInEx] Process bitness: 64-bit (x64)
[Info   :   BepInEx] Running under Unity 2022.2.16f1
[Info   :   BepInEx] Runtime version: 6.0.7
[Info   :   BepInEx] Runtime information: .NET 6.0.7
[Info   : Preloader] 0 patcher plugins loaded
[Info   : Preloader] 0 assemblies discovered
[Message:AssemblyPatcher] Executing 0 patch(es)
[Message:   BepInEx] Chainloader initialized
[Warning:Il2CppInterop] Class::Init signatures have been exhausted, using a substitute!
[Info   :Il2CppInterop] Registered mono type Il2CppInterop.Runtime.DelegateSupport+Il2CppToMonoDelegateReference in il2cpp domain
[Info   :   BepInEx] 1 plugin to load
[Info   :   BepInEx] Loading [ReiHook for Sons of the Forest - UnKnoWnCheaTs.me 2.0.0]
[Info   :ReiHook for Sons of the Forest - UnKnoWnCheaTs.me] Registering C# Object's...
[Info   :Il2CppInterop] Registered mono type ReiHook.UI.Menu in il2cpp domain
[Info   :Il2CppInterop] Registered mono type ReiHook.Features._Player.Transform in il2cpp domain
[Info   :Il2CppInterop] Registered mono type ReiHook.Features._Player.Health in il2cpp domain
[Info   :Il2CppInterop] Registered mono type ReiHook.Features._Player.Stamina in il2cpp domain
[Info   :Il2CppInterop] Registered mono type ReiHook.Features._Player.Strength in il2cpp domain
[Info   :Il2CppInterop] Registered mono type ReiHook.Features._Player.Hunger in il2cpp domain
[Info   :Il2CppInterop] Registered mono type ReiHook.Features._Player.Thirst in il2cpp domain
[Info   :Il2CppInterop] Registered mono type ReiHook.Features._Player.Tired in il2cpp domain
[Info   :Il2CppInterop] Registered mono type ReiHook.Features._Player.Cold in il2cpp domain
[Info   :Il2CppInterop] Registered mono type ReiHook.Features._Visuals.ESP in il2cpp domain
[Info   :Il2CppInterop] Registered mono type ReiHook.Features._Visuals.Radar in il2cpp domain
[Info   :Il2CppInterop] Registered mono type ReiHook.Features._Weapon.UnlimitedAmmo in il2cpp domain
[Info   :Il2CppInterop] Registered mono type ReiHook.Features._Weapon.RapidFire in il2cpp domain
[Info   :Il2CppInterop] Registered mono type ReiHook.Features._Weapon.InfiniteRopeLength in il2cpp domain
[Info   :ReiHook for Sons of the Forest - UnKnoWnCheaTs.me] Registering Harmony Patches...
[Message:   BepInEx] Chainloader startup complete
