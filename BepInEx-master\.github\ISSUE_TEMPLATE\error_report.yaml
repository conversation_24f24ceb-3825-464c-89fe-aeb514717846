name: Error Report
description: Unsure if an error is a bug? Post an error message or error log you got from BepInEx
labels: [error, needs-replication]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to send over an error!
        Note that while errors can be logged as BepInEx errors, they might not be related to BepInEx itself.
        
        If you have an issue with a plugin, contact the plugin developer first.
  - type: textarea
    id: basic-description
    attributes:
      label: What happened?
      description: Please describe what you did to get the error?
      placeholder: Tell us what you did!
    validations:
      required: true
  - type: dropdown
    id: bepin-distro
    attributes:
      label: BepInEx Distribution
      description: What BepInEx build are you using?
      options:
        - Stable from GitHub
        - Bleeding Edge from BepisBuilds
        - 3rd party distribution
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: Log outputs
      description: |
        Please copy and paste any log output or error message.
        You can use **one or multiple** of the following log sources:
        
        - BepInEx Log: located in `BepInEx/LogOutput.log`
        - Preloader error log: located in game folder and named `preloader_<date>.log`
        - Unity Player log: `output_log.txt` or `Player.log` generated by Unity
        - Copy-paste from BepInEx console if enabled
      render: txt
    validations:
      required: true
  - type: textarea
    id: env
    attributes:
      label: Environment
      description: |
        Fill in information about your game and precise BepInEx version  
        Example:
          - **OS**: Ubuntu 20.04
          - **BepInEx**: 5.4.13
          - **Game**: Risk of Rain 2
        
        You can check BepInEx version by checking version of `BepInEx/core/BepInEx.dll` or `BepInEx/core/BepInEx.Core.dll`.
      value: |
          - OS: 
          - BepInEx: 
          - Game: 
      render: markdown
    validations:
      required: true
