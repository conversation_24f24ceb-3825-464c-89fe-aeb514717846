@echo off
title Sons of the Forest - Enhanced Mod Launcher
color 0B
echo.
echo ========================================
echo   Sons of the Forest - Enhanced Mod Launcher
echo ========================================
echo.

REM Comprehensive pre-flight checks
echo [36mPerforming pre-flight checks...[0m
echo.

REM Check if SOTFmenu.dll exists
if not exist "SOTFmenu.dll" (
    echo [31m✗ SOTFmenu.dll not found![0m
    echo [33mPlease run download_helper.bat to get the mod files.[0m
    echo.
    set /p helper="Run download helper now? (y/n): "
    if /i "%helper%"=="y" call download_helper.bat
    pause
    exit /b 1
) else (
    echo [32m✓ SOTFmenu.dll found[0m
)

REM Check if game executable exists
if not exist "SonsOfTheForest.exe" (
    echo [31m✗ SonsOfTheForest.exe not found![0m
    echo [33mMake sure this script is in the game directory.[0m
    echo [37mCurrent directory: %CD%[0m
    echo.
    pause
    exit /b 1
) else (
    echo [32m✓ Game executable found[0m
)

REM Check if SOTF config folder exists
if not exist "%USERPROFILE%\Documents\SOTF" (
    echo [33m! Creating SOTF configuration folder...[0m
    mkdir "%USERPROFILE%\Documents\SOTF"
    echo [32m✓ Configuration folder created[0m
) else (
    echo [32m✓ Configuration folder exists[0m
)

REM Check for config file
if not exist "%USERPROFILE%\Documents\SOTF\config.ini" (
    echo [33m! Configuration file missing, creating default...[0m
    call install_sotf_menu.bat
)

echo.
echo [32m🎮 READY TO LAUNCH![0m
echo.
echo [36mIMPORTANT REMINDERS:[0m
echo [37m• Press 'P' to open/close the mod menu[0m
echo [37m• Set game to Borderless Window mode (NOT fullscreen)[0m
echo [37m• Close overlay programs (MSI Afterburner, RTSS, etc.)[0m
echo [37m• If cursor gets stuck, press Windows key or Alt+Tab[0m
echo.

REM Check if user wants to proceed
set /p proceed="Launch Sons of the Forest with mods? (y/n): "
if /i not "%proceed%"=="y" (
    echo [37mLaunch cancelled.[0m
    pause
    exit /b 0
)

echo.
echo [36mStarting Sons of the Forest with mod support...[0m
echo [33mGame will launch in 3 seconds...[0m
timeout /t 3 /nobreak >nul

REM Start the game
start "" "SonsOfTheForest.exe"

echo.
echo [32m✓ Game launched successfully![0m
echo.
echo [36mMOD MENU USAGE:[0m
echo [37m• Wait for game to fully load[0m
echo [37m• Press 'P' to open the mod menu[0m
echo [37m• Navigate with mouse[0m
echo [37m• Enable features as desired[0m
echo.
echo [33mTROUBLESHOOTING:[0m
echo [37m• Menu not opening? Check Borderless Window mode[0m
echo [37m• Cursor stuck? Press Windows key + click game[0m
echo [37m• Game crashes? Update mod or verify game files[0m
echo [37m• Antivirus blocking? Add exception for SOTFmenu.dll[0m
echo.
echo [36mEnjoy your enhanced Sons of the Forest experience![0m
echo.
pause
