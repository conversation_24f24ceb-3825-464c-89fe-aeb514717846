@echo off
title Sons of the Forest - Mod Menu Launcher
echo.
echo ========================================
echo   Sons of the Forest Mod Menu Launcher
echo ========================================
echo.

REM Check if SOTFmenu.dll exists
if not exist "SOTFmenu.dll" (
    echo ERROR: SOTFmenu.dll not found in current directory!
    echo Please download and extract the SOTFmenu files here.
    echo Download from: https://www.unknowncheats.me/forum/downloads.php?do=file^&id=42183
    echo.
    pause
    exit /b 1
)

REM Check if game executable exists
if not exist "SonsOfTheForest.exe" (
    echo ERROR: SonsOfTheForest.exe not found!
    echo Make sure this script is in the game directory.
    echo.
    pause
    exit /b 1
)

REM Check if SOTF config folder exists
if not exist "%USERPROFILE%\Documents\SOTF" (
    echo Creating SOTF configuration folder...
    mkdir "%USERPROFILE%\Documents\SOTF"
)

echo Starting Sons of the Forest with mod menu...
echo.
echo CONTROLS:
echo - Press 'P' to open/close the mod menu
echo - Make sure game is in Borderless Window mode
echo - Close any overlay programs (MSI Afterburner, RTSS, etc.)
echo.
echo Starting game in 3 seconds...
timeout /t 3 /nobreak >nul

REM Start the game
start "" "SonsOfTheForest.exe"

echo Game started! 
echo.
echo If the mod menu doesn't work:
echo 1. Make sure you have the latest version of SOTFmenu.dll
echo 2. Try running as administrator
echo 3. Check that no antivirus is blocking the mod
echo 4. Ensure game is in Borderless Window mode
echo.
pause
