@echo off
title ReiHook Crash Diagnostics
color 0C
echo.
echo ========================================
echo   ReiHook Crash Diagnostics
echo ========================================
echo.

echo [36mAnalyzing crash and system status...[0m
echo.

REM Check if game is still running
tasklist /fi "imagename eq SonsOfTheForest.exe" 2>NUL | find /i /n "SonsOfTheForest.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [33m! Game is still running[0m
    echo [37m  The crash may have been a temporary issue[0m
) else (
    echo [32m✓ Game process has ended[0m
)

echo.
echo [36m=== BEPINEX STATUS ===[0m

if exist "BepInEx\LogOutput.log" (
    echo [32m✓ BepInEx log found[0m
    echo [37m  Last few lines:[0m
    tail -n 5 "BepInEx\LogOutput.log" 2>nul || (
        echo [37m  Showing end of log:[0m
        powershell -Command "Get-Content 'BepInEx\LogOutput.log' | Select-Object -Last 5"
    )
) else (
    echo [31m✗ BepInEx log not found[0m
)

echo.
if exist "BepInEx\ErrorLog.log" (
    echo [33m! Error log exists[0m
    echo [37m  Contents:[0m
    type "BepInEx\ErrorLog.log"
) else (
    echo [32m✓ No error log (good sign)[0m
)

echo.
echo [36m=== REIHOOK STATUS ===[0m

if exist "BepInEx\plugins\ReiHook.dll" (
    echo [32m✓ ReiHook mod installed[0m
    for %%A in ("BepInEx\plugins\ReiHook.dll") do echo [37m  Size: %%~zA bytes[0m
) else (
    echo [31m✗ ReiHook mod missing[0m
)

echo.
echo [36m=== COMMON CRASH CAUSES ===[0m
echo.

echo [33mPossible reasons for crash:[0m
echo [37m1. Game version incompatibility[0m
echo [37m2. Fullscreen mode (should be Borderless Window)[0m
echo [37m3. Overlay programs interfering[0m
echo [37m4. Antivirus blocking mod[0m
echo [37m5. Insufficient permissions[0m
echo [37m6. Memory issues[0m
echo.

echo [36m=== SOLUTIONS TO TRY ===[0m
echo.

echo [32m1. LAUNCH IN BORDERLESS WINDOW:[0m
echo [37m   • Start game normally[0m
echo [37m   • Go to Settings → Graphics[0m
echo [37m   • Set Display Mode to Borderless Window[0m
echo [37m   • Apply and restart[0m
echo.

echo [32m2. RUN AS ADMINISTRATOR:[0m
echo [37m   • Right-click game launcher[0m
echo [37m   • Select "Run as administrator"[0m
echo.

echo [32m3. DISABLE OVERLAYS:[0m
echo [37m   • Close MSI Afterburner[0m
echo [37m   • Close RTSS[0m
echo [37m   • Disable Discord overlay[0m
echo [37m   • Disable Steam overlay[0m
echo.

echo [32m4. CHECK ANTIVIRUS:[0m
echo [37m   • Add game folder to exceptions[0m
echo [37m   • Check quarantine for ReiHook.dll[0m
echo.

echo [32m5. VERIFY GAME FILES:[0m
echo [37m   • Open Steam[0m
echo [37m   • Right-click Sons of the Forest[0m
echo [37m   • Properties → Local Files → Verify integrity[0m
echo.

set /p retry="Would you like to try launching the game again? (y/n): "
if /i "%retry%"=="y" (
    echo.
    echo [36mLaunching game with crash monitoring...[0m
    echo [33mRemember: Press F4 to open ReiHook menu![0m
    start "" "SonsOfTheForest.exe"
    echo.
    echo [32mGame launched. Monitor for crashes and check BepInEx logs.[0m
) else (
    echo.
    echo [37mDiagnostics complete. Address the issues above and try again.[0m
)

echo.
pause
