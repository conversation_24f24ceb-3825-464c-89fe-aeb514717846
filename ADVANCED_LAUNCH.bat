@echo off
title Sons of the Forest - Advanced Mod Launcher
color 0A
echo.
echo ========================================
echo   Advanced SOTFmenu Launcher
echo ========================================
echo.

REM Check for admin rights
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [32m✓ Running as Administrator[0m
) else (
    echo [33m! Not running as Administrator[0m
    echo [37m  This may cause injection issues[0m
    echo.
    set /p admin="Restart as Administrator? (recommended) (y/n): "
    if /i "!admin!"=="y" (
        echo [36mRestarting as Administrator...[0m
        powershell -Command "Start-Process '%~f0' -Verb RunAs"
        exit /b 0
    )
)

echo.
echo Checking mod files...

if not exist "SOTFmenu.dll" (
    echo [31m✗ SOTFmenu.dll not found![0m
    echo Please make sure the mod file is in this directory.
    pause
    exit /b 1
) else (
    echo [32m✓ SOTFmenu.dll found[0m
)

if not exist "SonsOfTheForest.exe" (
    echo [31m✗ Game executable not found![0m
    pause
    exit /b 1
) else (
    echo [32m✓ Game executable found[0m
)

echo.
echo [36mCRITICAL SETUP CHECKLIST:[0m
echo.
echo [37m□ Close ALL overlay programs (MSI Afterburner, RTSS, Discord, Steam)[0m
echo [37m□ Add antivirus exception for this folder[0m
echo [37m□ Set game to Borderless Window mode (NOT fullscreen)[0m
echo [37m□ Make sure Steam is running[0m
echo.

set /p ready="Have you completed the checklist above? (y/n): "
if /i not "%ready%"=="y" (
    echo [33mPlease complete the checklist first, then run this launcher again.[0m
    pause
    exit /b 0
)

echo.
echo [36mLaunching Sons of the Forest with mod injection...[0m
echo.

REM Kill any existing game processes
taskkill /f /im "SonsOfTheForest.exe" >nul 2>&1

REM Wait a moment
timeout /t 2 /nobreak >nul

echo [37mStarting game process...[0m
start "" "SonsOfTheForest.exe"

echo [37mWaiting for game to initialize...[0m
timeout /t 5 /nobreak >nul

echo.
echo [32m✓ Game launched![0m
echo.
echo [36mMOD MENU INSTRUCTIONS:[0m
echo [37m• Wait for the game to FULLY load (main menu visible)[0m
echo [37m• Try these keys to open the mod menu:[0m
echo [37m  - Press 'P' (primary key)[0m
echo [37m  - Press 'INSERT' (alternative)[0m
echo [37m  - Press 'HOME' (alternative)[0m
echo [37m  - Press 'F1' (alternative)[0m
echo.
echo [33mIf menu still doesn't open:[0m
echo [37m• Make sure game is in Borderless Window mode[0m
echo [37m• Try running this launcher as Administrator[0m
echo [37m• Check if antivirus blocked the mod[0m
echo [37m• Close all overlay programs[0m
echo.

echo [36mMod injection attempt complete.[0m
echo [37mThe mod should be active if the game loaded successfully.[0m
echo.
pause
