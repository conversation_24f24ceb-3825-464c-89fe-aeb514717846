@echo off
title SOTFmenu Final Setup
color 0A
echo.
echo ========================================
echo   SOTFmenu Final Setup Assistant
echo ========================================
echo.

echo Checking system...
if not exist "SonsOfTheForest.exe" (
    echo ERROR: Not in Sons of the Forest directory!
    echo Please run this from the game folder.
    pause
    exit /b 1
)

echo Creating configuration...
if not exist "%USERPROFILE%\Documents\SOTF" mkdir "%USERPROFILE%\Documents\SOTF"

echo [Settings] > "%USERPROFILE%\Documents\SOTF\config.ini"
echo GamePath=%CD%\SonsOfTheForest.exe >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo OpenCloseKey=P >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo ShowTerminal=false >> "%USERPROFILE%\Documents\SOTF\config.ini"

echo.
echo Configuration created successfully!
echo.

if exist "SOTFmenu.dll" (
    echo SOTFmenu.dll found - Ready to play!
    echo.
    echo LAUNCH OPTIONS:
    echo 1. Run launch_with_mod.bat
    echo 2. Or start game manually and press P for menu
    echo.
) else (
    echo SOTFmenu.dll NOT FOUND
    echo.
    echo MANUAL STEPS REQUIRED:
    echo 1. Go to: https://www.unknowncheats.me/forum/downloads.php?do=file^&id=42183
    echo 2. Register/login to download SOTFmenu v0.7.7
    echo 3. Extract zip and copy SOTFmenu.dll here
    echo 4. Run this script again
    echo.
    echo Opening download page...
    start "" "https://www.unknowncheats.me/forum/downloads.php?do=file&id=42183"
)

echo.
echo IMPORTANT:
echo - Set game to Borderless Window mode
echo - Close overlay programs
echo - Press P in-game for mod menu
echo.
pause
