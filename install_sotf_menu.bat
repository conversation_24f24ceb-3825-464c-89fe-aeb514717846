@echo off
echo Installing SOTFmenu for Sons of the Forest...
echo.

REM Create SOTF folder in Documents
if not exist "%USERPROFILE%\Documents\SOTF" (
    mkdir "%USERPROFILE%\Documents\SOTF"
    echo Created SOTF folder in Documents
)

REM Create basic config file
echo [Settings] > "%USERPROFILE%\Documents\SOTF\config.ini"
echo GamePath=%~dp0SonsOfTheForest.exe >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo OpenCloseKey=P >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo ShowTerminal=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo MenuTheme=0 >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo.
echo [Features] >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo GodMode=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo InfiniteStamina=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo NoHungry=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo NoDehydration=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo NoSleep=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo InfiniteAmmo=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo SuperJump=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo NoFallDamage=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo NoGravity=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo InstantBuild=false >> "%USERPROFILE%\Documents\SOTF\config.ini"

echo Configuration file created successfully!
echo.
echo NEXT STEPS:
echo 1. Download SOTFmenu v0.7.7 from: https://www.unknowncheats.me/forum/downloads.php?do=file^&id=42183
echo 2. Extract the downloaded zip file
echo 3. Copy SOTFmenu.dll to this game directory
echo 4. Run "Launch SOTF Mod Menu.exe" from the extracted files
echo.
echo IMPORTANT NOTES:
echo - Set the game to Borderless Window mode (NOT fullscreen)
echo - Default menu key is 'P'
echo - Close overlay programs like MSI Afterburner/RTSS
echo - Use at your own risk - may cause bans on public servers
echo.
pause
