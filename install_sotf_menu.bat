@echo off
title SOTFmenu Installation Assistant
color 0A
echo.
echo ========================================
echo   SOTFmenu Installation Assistant
echo ========================================
echo.

REM Create SOTF folder in Documents
if not exist "%USERPROFILE%\Documents\SOTF" (
    mkdir "%USERPROFILE%\Documents\SOTF"
    echo [32m✓ Created SOTF folder in Documents[0m
) else (
    echo [33m! SOTF folder already exists[0m
)

REM Create comprehensive config file
echo [Settings] > "%USERPROFILE%\Documents\SOTF\config.ini"
echo GamePath=%~dp0SonsOfTheForest.exe >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo OpenCloseKey=P >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo ShowTerminal=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo MenuTheme=0 >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo. >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo [Features] >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo GodMode=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo InfiniteStamina=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo NoHungry=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo NoDehydration=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo NoSleep=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo InfiniteAmmo=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo SuperJump=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo NoFallDamage=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo NoGravity=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo InstantBuild=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo. >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo [Movement] >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo CustomSpeed=1.0 >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo CustomSwimSpeed=1.0 >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo. >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo [Environment] >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo Season=0 >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo TreeRegrowRate=1.0 >> "%USERPROFILE%\Documents\SOTF\config.ini"
echo DayTimeControl=12.0 >> "%USERPROFILE%\Documents\SOTF\config.ini"

echo [32m✓ Configuration file created successfully![0m
echo.

REM Check if mod file exists
if exist "SOTFmenu.dll" (
    echo [32m✓ SOTFmenu.dll found - Ready to launch![0m
    echo.
    echo [36mYou can now:[0m
    echo [37m- Run launch_with_mod.bat to start the game[0m
    echo [37m- Press 'P' in-game to open the mod menu[0m
    echo.
) else (
    echo [31m✗ SOTFmenu.dll not found[0m
    echo.
    echo [33mMANUAL STEPS REQUIRED:[0m
    echo [37m1. Open your web browser[0m
    echo [37m2. Go to: https://www.unknowncheats.me/forum/downloads.php?do=file^&id=42183[0m
    echo [37m3. Register/login to download SOTFmenu v0.7.7[0m
    echo [37m4. Extract the zip file[0m
    echo [37m5. Copy SOTFmenu.dll to this directory[0m
    echo [37m6. Run this script again to verify[0m
    echo.
    echo [36mOpening download page in your browser...[0m
    start "" "https://www.unknowncheats.me/forum/downloads.php?do=file&id=42183"
)

echo [33mIMPORTANT GAME SETTINGS:[0m
echo [37m- Set game to Borderless Window mode (NOT fullscreen)[0m
echo [37m- Close overlay programs (MSI Afterburner, RTSS, etc.)[0m
echo [37m- Default menu key is 'P'[0m
echo [37m- Use at your own risk on multiplayer servers[0m
echo.
pause
