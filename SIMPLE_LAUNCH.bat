@echo off
title Sons of the Forest with SOTFmenu
echo.
echo ========================================
echo   Sons of the Forest - Mod Launcher
echo ========================================
echo.

if not exist "SOTFmenu.dll" (
    echo ERROR: SOTFmenu.dll not found!
    echo Please make sure the mod file is in this directory.
    pause
    exit /b 1
)

if not exist "SonsOfTheForest.exe" (
    echo ERROR: Game executable not found!
    echo Please run this from the game directory.
    pause
    exit /b 1
)

echo SOTFmenu mod detected - Ready to launch!
echo.
echo IMPORTANT REMINDERS:
echo - Set game to Borderless Window mode (NOT fullscreen)
echo - Close overlay programs (MSI Afterburner, RTSS, etc.)
echo - Press 'P' in-game to open the mod menu
echo.

set /p launch="Launch Sons of the Forest with mods? (y/n): "
if /i not "%launch%"=="y" (
    echo Launch cancelled.
    pause
    exit /b 0
)

echo.
echo Starting Sons of the Forest...
echo The mod menu will be available once the game loads.
echo.

start "" "SonsOfTheForest.exe"

echo.
echo Game launched successfully!
echo.
echo MOD USAGE:
echo - Wait for the game to fully load
echo - Press 'P' to open the mod menu
echo - Use mouse to navigate the menu
echo - Enable features as desired
echo.
echo If you have issues:
echo - Make sure game is in Borderless Window mode
echo - If cursor gets stuck, press Windows key then click game
echo - Check that antivirus isn't blocking the mod
echo.
pause
