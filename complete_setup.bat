@echo off
title SOTFmenu Complete Setup Assistant
color 0A
cls

echo.
echo ████████████████████████████████████████████████████████████████
echo █                                                              █
echo █           SOTFmenu Complete Setup Assistant                 █
echo █                                                              █
echo █  This script will guide you through the entire installation █
echo █  process for the Sons of the Forest mod menu.              █
echo █                                                              █
echo ████████████████████████████████████████████████████████████████
echo.

echo [33m⚠️  IMPORTANT DISCLAIMER:[0m
echo [37mThis mod may:[0m
echo [37m• Get you banned from public multiplayer servers[0m
echo [37m• Be flagged by antivirus software (false positive)[0m
echo [37m• Break after game updates[0m
echo [37m• Affect game stability[0m
echo.
set /p agree="Do you understand and accept these risks? (y/n): "
if /i not "%agree%"=="y" (
    echo [37mSetup cancelled. Exiting...[0m
    pause
    exit /b 0
)

cls
echo.
echo ========================================
echo   STEP 1: SYSTEM PREPARATION
echo ========================================
echo.

echo [36mChecking system requirements...[0m

REM Check if we're in the right directory
if not exist "SonsOfTheForest.exe" (
    echo [31m✗ Not in Sons of the Forest directory![0m
    echo [33mPlease run this script from:[0m
    echo [37m%PROGRAMFILES(X86)%\Steam\steamapps\common\Sons Of The Forest[0m
    echo.
    pause
    exit /b 1
) else (
    echo [32m✓ Correct game directory[0m
)

REM Create Documents folder structure
if not exist "%USERPROFILE%\Documents\SOTF" (
    mkdir "%USERPROFILE%\Documents\SOTF"
    echo [32m✓ Created SOTF configuration folder[0m
) else (
    echo [32m✓ SOTF configuration folder exists[0m
)

echo.
echo ========================================
echo   STEP 2: CONFIGURATION SETUP
echo ========================================
echo.

echo [36mCreating configuration files...[0m

REM Create comprehensive config
call install_sotf_menu.bat >nul 2>&1
echo [32m✓ Configuration files created[0m

echo.
echo ========================================
echo   STEP 3: MOD FILE CHECK
echo ========================================
echo.

if exist "SOTFmenu.dll" (
    echo [32m✓ SOTFmenu.dll found - Ready to go![0m
    set mod_ready=1
) else (
    echo [31m✗ SOTFmenu.dll not found[0m
    echo.
    echo [33mYou need to manually download the mod files:[0m
    echo.
    echo [36m1. DOWNLOAD:[0m
    echo [37m   • Go to: https://www.unknowncheats.me/forum/downloads.php?do=file^&id=42183[0m
    echo [37m   • Register/login (free account)[0m
    echo [37m   • Download SOTFmenu v0.7.7[0m
    echo.
    echo [36m2. EXTRACT:[0m
    echo [37m   • Extract the downloaded zip file[0m
    echo [37m   • Find SOTFmenu.dll in the extracted files[0m
    echo.
    echo [36m3. COPY:[0m
    echo [37m   • Copy SOTFmenu.dll to this directory[0m
    echo [37m   • Copy Launch SOTF Mod Menu.exe (optional)[0m
    echo.
    
    set /p open_page="Open download page in browser? (y/n): "
    if /i "%open_page%"=="y" (
        start "" "https://www.unknowncheats.me/forum/downloads.php?do=file&id=42183"
        echo [32mDownload page opened in browser[0m
    )
    
    echo.
    echo [33mAfter downloading and copying files, run this script again.[0m
    set mod_ready=0
)

echo.
echo ========================================
echo   STEP 4: GAME CONFIGURATION
echo ========================================
echo.

echo [36mIMPORTANT GAME SETTINGS:[0m
echo [37m• Launch Sons of the Forest[0m
echo [37m• Go to Settings → Graphics[0m
echo [37m• Set Display Mode to "Borderless Window"[0m
echo [37m• Close overlay programs (MSI Afterburner, RTSS)[0m
echo.

if "%mod_ready%"=="1" (
    echo ========================================
    echo   STEP 5: LAUNCH OPTIONS
    echo ========================================
    echo.
    
    echo [32m🎉 SETUP COMPLETE![0m
    echo.
    echo [36mChoose how to launch:[0m
    echo [37m1. Use enhanced launcher (recommended)[0m
    echo [37m2. Use original mod launcher (if available)[0m
    echo [37m3. Manual launch[0m
    echo [37m4. Exit and launch later[0m
    echo.
    
    set /p launch_choice="Enter your choice (1-4): "
    
    if "%launch_choice%"=="1" (
        echo [36mLaunching with enhanced launcher...[0m
        call launch_with_mod.bat
    ) else if "%launch_choice%"=="2" (
        if exist "Launch SOTF Mod Menu.exe" (
            echo [36mLaunching with original mod launcher...[0m
            start "" "Launch SOTF Mod Menu.exe"
        ) else (
            echo [31mOriginal launcher not found. Using enhanced launcher...[0m
            call launch_with_mod.bat
        )
    ) else if "%launch_choice%"=="3" (
        echo [36mStarting game manually...[0m
        start "" "SonsOfTheForest.exe"
        echo [33mRemember: Press 'P' in-game to open the mod menu[0m
    ) else (
        echo [37mSetup complete. Run launch_with_mod.bat when ready to play.[0m
    )
) else (
    echo ========================================
    echo   SETUP INCOMPLETE
    echo ========================================
    echo.
    echo [33mPlease complete the manual download steps above,[0m
    echo [33mthen run this script again.[0m
)

echo.
echo [36mSetup assistant complete![0m
pause
