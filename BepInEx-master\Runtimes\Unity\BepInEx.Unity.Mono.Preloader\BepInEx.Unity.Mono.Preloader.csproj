﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFrameworks>net35;netstandard2.0</TargetFrameworks>
        <AppendTargetFrameworkToOutputPath>true</AppendTargetFrameworkToOutputPath>
        <Description>BepInEx preloader module for UnityMono games</Description>
    </PropertyGroup>
    <PropertyGroup Condition="'$(TargetFramework)' == 'net35'">
        <!-- Output only net35 version to distribution; netstadnard is only used for generating NuGet package -->
        <OutputPath>$(BuildDir)/Unity.Mono</OutputPath>
        <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    </PropertyGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\..\BepInEx.Core\BepInEx.Core.csproj"/>
        <ProjectReference Include="..\..\..\BepInEx.Preloader.Core\BepInEx.Preloader.Core.csproj"/>
        <ProjectReference Include="..\BepInEx.Unity.Common\BepInEx.Unity.Common.csproj"/>
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.NETFramework.ReferenceAssemblies" Version="1.0.3" PrivateAssets="All"/>
        <PackageReference Include="HarmonyX" Version="2.10.2" />
    </ItemGroup>
</Project>
