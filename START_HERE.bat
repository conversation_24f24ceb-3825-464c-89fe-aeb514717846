@echo off
title SOTFmenu - Start Here
color 0B
cls
echo.
echo ████████████████████████████████████████████
echo █                                          █
echo █        SOTFmenu Installation            █
echo █           START HERE                    █
echo █                                          █
echo ████████████████████████████████████████████
echo.
echo Welcome! This will install the SOTFmenu mod for Sons of the Forest.
echo.
echo ⚠️  DISCLAIMER: This mod may get you banned from public servers.
echo    Use at your own risk!
echo.
echo What would you like to do?
echo.
echo [1] Complete automated setup (recommended)
echo [2] Quick setup (minimal steps)
echo [3] Download helper only
echo [4] Launch game with existing mod
echo [5] Read documentation
echo [6] Exit
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" (
    echo.
    echo Starting complete automated setup...
    call FINAL_SETUP.bat
) else if "%choice%"=="2" (
    echo.
    echo Starting quick setup...
    call install_sotf_menu.bat
) else if "%choice%"=="3" (
    echo.
    echo Opening download helper...
    call download_helper.bat
) else if "%choice%"=="4" (
    echo.
    echo Launching game with mod...
    call launch_with_mod.bat
) else if "%choice%"=="5" (
    echo.
    echo Opening documentation...
    start notepad "SOTF_MOD_MENU_README.txt"
    start notepad "INSTALLATION_COMPLETE.txt"
) else if "%choice%"=="6" (
    echo.
    echo Goodbye!
    exit /b 0
) else (
    echo.
    echo Invalid choice. Please run again and select 1-6.
    pause
    exit /b 1
)

echo.
echo Done! Check the output above for next steps.
pause
