@echo off
title BepInEx Auto-Installer
color 0B
echo.
echo ========================================
echo   BepInEx Auto-Installer for SOTF
echo ========================================
echo.

echo [36mThis will install BepInEx framework required for ReiHook mod.[0m
echo.

REM Check if already installed
if exist "BepInEx" (
    echo [32m✓ BepInEx already installed[0m
    if exist "BepInEx\plugins" (
        echo [32m✓ Plugins folder exists[0m
        echo.
        echo [36mBepInEx is ready! You can now install ReiHook.[0m
        set /p install_reihook="Install ReiHook mod now? (y/n): "
        if /i "%install_reihook%"=="y" (
            call INSTALL_REIHOOK.bat
        )
        pause
        exit /b 0
    )
)

echo [33mBepInEx Installation Methods:[0m
echo.
echo [37m1. AUTOMATIC (Recommended)[0m
echo [37m   - Downloads and installs BepInEx automatically[0m
echo [37m   - Requires PowerShell and internet connection[0m
echo.
echo [37m2. MANUAL[0m
echo [37m   - Opens download page for manual installation[0m
echo [37m   - You download and extract manually[0m
echo.

set /p method="Choose installation method (1 for auto, 2 for manual): "

if "%method%"=="1" (
    echo.
    echo [36mStarting automatic BepInEx installation...[0m
    echo.
    
    REM Create temp directory
    if not exist "temp_bepinex" mkdir "temp_bepinex"
    
    echo [37mDownloading BepInEx 6.0.0 IL2CPP...[0m
    powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://github.com/BepInEx/BepInEx/releases/download/v6.0.0-be.674%2B82077ec/BepInEx_UnityIL2CPP_x64_6.0.0-be.674.zip' -OutFile 'temp_bepinex\BepInEx.zip'}"
    
    if exist "temp_bepinex\BepInEx.zip" (
        echo [32m✓ Download completed[0m
        echo [37mExtracting BepInEx...[0m
        
        powershell -Command "Expand-Archive -Path 'temp_bepinex\BepInEx.zip' -DestinationPath '.' -Force"
        
        if exist "BepInEx" (
            echo [32m✓ BepInEx extracted successfully[0m
            
            REM Clean up
            rmdir /s /q "temp_bepinex"
            
            echo [32m✓ BepInEx installation complete![0m
            echo.
            echo [36mNext steps:[0m
            echo [37m1. Run the game once to initialize BepInEx[0m
            echo [37m2. Then install ReiHook mod[0m
            echo.
            
            set /p run_game="Run game now to initialize BepInEx? (y/n): "
            if /i "%run_game%"=="y" (
                echo [36mLaunching game for BepInEx initialization...[0m
                start "" "SonsOfTheForest.exe"
                echo.
                echo [33mLet the game load completely, then close it.[0m
                echo [33mAfter that, run INSTALL_REIHOOK.bat to install the mod.[0m
            )
        ) else (
            echo [31m✗ Extraction failed[0m
            rmdir /s /q "temp_bepinex"
        )
    ) else (
        echo [31m✗ Download failed[0m
        echo [33mTrying manual installation method...[0m
        goto manual_install
    )
    
) else if "%method%"=="2" (
    :manual_install
    echo.
    echo [36mManual Installation Instructions:[0m
    echo.
    echo [37m1. Download BepInEx 6.0+ from GitHub[0m
    echo [37m2. Look for "BepInEx_UnityIL2CPP_x64" version[0m
    echo [37m3. Extract ALL contents to this game directory[0m
    echo [37m4. Run the game once to initialize[0m
    echo [37m5. Then run INSTALL_REIHOOK.bat[0m
    echo.
    
    start "" "https://github.com/BepInEx/BepInEx/releases"
    echo [32mDownload page opened in browser[0m
    
) else (
    echo [31mInvalid choice. Please run again and select 1 or 2.[0m
)

echo.
pause
