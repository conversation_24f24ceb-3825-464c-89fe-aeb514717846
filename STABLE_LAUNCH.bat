@echo off
title Sons of the Forest - Stable Launch
color 0B
echo.
echo ========================================
echo   Stable Launch with Crash Prevention
========================================
echo.

echo [36mApplying crash prevention measures...[0m
echo.

REM Kill any existing game processes
taskkill /f /im "SonsOfTheForest.exe" >nul 2>&1

REM Check for common crash causes
echo [33mChecking for crash causes:[0m

REM Check overlay programs
tasklist /fi "imagename eq MSIAfterburner.exe" 2>NUL | find /i /n "MSIAfterburner.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [31m✗ MSI Afterburner running - this can cause crashes[0m
    set /p kill_msi="Kill MSI Afterburner? (y/n): "
    if /i "!kill_msi!"=="y" taskkill /f /im "MSIAfterburner.exe" >nul 2>&1
) else (
    echo [32m✓ MSI Afterburner not running[0m
)

tasklist /fi "imagename eq RTSS.exe" 2>NUL | find /i /n "RTSS.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [31m✗ RTSS running - this can cause crashes[0m
    set /p kill_rtss="Kill RTSS? (y/n): "
    if /i "!kill_rtss!"=="y" taskkill /f /im "RTSS.exe" >nul 2>&1
) else (
    echo [32m✓ RTSS not running[0m
)

echo.
echo [36mLaunch Options:[0m
echo [37m[1] Launch with ReiHook mod (may crash)[0m
echo [37m[2] Launch without mods (stable)[0m
echo [37m[3] Try compatibility mode[0m
echo [37m[4] Exit[0m
echo.

set /p choice="Choose option (1-4): "

if "%choice%"=="1" (
    echo.
    echo [33m⚠️  Launching with ReiHook mod...[0m
    echo [37mIf it crashes again, try option 2 or 3[0m
    echo.
    echo [36mCRITICAL REMINDERS:[0m
    echo [37m• Set game to Borderless Window mode IMMEDIATELY[0m
    echo [37m• Don't press F4 until you're fully in-game[0m
    echo [37m• Close any overlay programs[0m
    echo.
    
    timeout /t 3 /nobreak >nul
    start "" "SonsOfTheForest.exe"
    
) else if "%choice%"=="2" (
    echo.
    echo [36mLaunching without mods (stable mode)...[0m
    
    REM Temporarily rename ReiHook to disable it
    if exist "BepInEx\plugins\ReiHook.dll" (
        ren "BepInEx\plugins\ReiHook.dll" "ReiHook.dll.disabled"
        echo [33mReiHook temporarily disabled[0m
    )
    
    start "" "SonsOfTheForest.exe"
    
    echo.
    echo [32mGame launched without mods[0m
    echo [37mTo re-enable mods later, rename ReiHook.dll.disabled back to ReiHook.dll[0m
    
) else if "%choice%"=="3" (
    echo.
    echo [36mTrying compatibility mode...[0m
    
    REM Try running with compatibility settings
    echo [37mLaunching with Windows 10 compatibility...[0m
    start "" /WAIT cmd /c "SonsOfTheForest.exe"
    
) else if "%choice%"=="4" (
    echo [37mExiting...[0m
    exit /b 0
    
) else (
    echo [31mInvalid choice[0m
    pause
    exit /b 1
)

echo.
echo [32m✓ Launch attempt completed[0m
echo.
echo [36mIf the game crashed again:[0m
echo [37m• Try option 2 (launch without mods)[0m
echo [37m• Update your graphics drivers[0m
echo [37m• Verify game files in Steam[0m
echo [37m• Check Windows Event Viewer for crash details[0m
echo.
pause
