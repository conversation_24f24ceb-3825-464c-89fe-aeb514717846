{"root": [{"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__166149529", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "AstarPathfindingProject", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__3562739929", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "AstarPathfindingProject", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "<PERSON><PERSON>", "className": "BuildCommandBufferBase", "methodName": "InitStatics", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "<PERSON><PERSON>", "className": "OceanDebugGUI", "methodName": "InitStatics", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "<PERSON><PERSON>", "className": "TextureArrayHelpers", "methodName": "InitStatics", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "<PERSON><PERSON>", "className": "SphereWaterInteraction", "methodName": "InitStatics", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "<PERSON><PERSON>", "className": "LodDataMgr", "methodName": "InitStatics", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "<PERSON><PERSON>", "className": "LodDataMgrAnimWaves", "methodName": "InitStatics", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "<PERSON><PERSON>", "className": "LodDataMgrClipSurface", "methodName": "InitStatics", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "<PERSON><PERSON>", "className": "LodDataMgrDynWaves", "methodName": "InitStatics", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "<PERSON><PERSON>", "className": "LodDataMgrFlow", "methodName": "InitStatics", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "<PERSON><PERSON>", "className": "LodDataMgrFoam", "methodName": "InitStatics", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "<PERSON><PERSON>", "className": "LodDataMgrSeaFloorDepth", "methodName": "InitStatics", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "<PERSON><PERSON>", "className": "RegisterLodDataInputBase", "methodName": "InitStatics", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "<PERSON><PERSON>", "className": "LodDataMgrShadow", "methodName": "InitStatics", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "<PERSON><PERSON>", "className": "OceanChunk<PERSON><PERSON><PERSON>", "methodName": "InitStatics", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "<PERSON><PERSON>", "className": "OceanChunk<PERSON><PERSON><PERSON>", "methodName": "RunOnStart", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "<PERSON><PERSON>", "className": "<PERSON><PERSON><PERSON><PERSON>", "methodName": "InitStatics", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "<PERSON><PERSON>", "className": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "methodName": "InitStatics", "loadTypes": 4, "isUnityClass": false}, {"assemblyName": "Drawing", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__2705045610", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Drawing", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "FMOD.Unity", "nameSpace": "", "className": "FMOD_StudioSystem", "methodName": "ClearStatics", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "NWH.DWP2", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__3295892797", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "<PERSON><PERSON>", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__3482832046", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Sons", "nameSpace": "Sons.Multiplayer", "className": "SonsBoltSystem", "methodName": "RunOnStart", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Sons.GameApplication", "nameSpace": "Sons.GameApplication", "className": "Releases", "methodName": "Initialize", "loadTypes": 3, "isUnityClass": false}, {"assemblyName": "Sons.GameApplication", "nameSpace": "Sons.GameApplication", "className": "Version", "methodName": "Initialize", "loadTypes": 3, "isUnityClass": false}, {"assemblyName": "Sons.GameApplication", "nameSpace": "Sons.Utils", "className": "CommandLineArgs", "methodName": "ProcessArgs", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Sons.Gameplay.GameSetup", "nameSpace": "Sons.Save", "className": "NamedIntData", "methodName": "StaticInitialize", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Sons.Input", "nameSpace": "Sons.Input", "className": "InputSystem", "methodName": "ClearOldInstance", "loadTypes": 2, "isUnityClass": false}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__74814542", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.InputSystem", "nameSpace": "UnityEngine.InputSystem", "className": "InputSystem", "methodName": "RunInitializeInPlayer", "loadTypes": 1, "isUnityClass": true}, {"assemblyName": "Unity.MemoryProfiler", "nameSpace": "Unity.MemoryProfiler", "className": "MetadataInjector", "methodName": "PlayerInitMetadata", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.BatchRendererGroup-Testing", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__1606518823", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Experimental.Rendering", "className": "XRSystem", "methodName": "XRSystemInit", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Rendering", "className": "DebugUpdater", "methodName": "RuntimeInit", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.HighDefinition.Runtime", "nameSpace": "UnityEngine.Rendering.HighDefinition", "className": "HDRuntimeReflectionSystem", "methodName": "Initialize", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.HighDefinition.Runtime", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__4291790609", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Splines", "nameSpace": "", "className": "__JobReflectionRegistrationOutput__2502060409", "methodName": "EarlyInit", "loadTypes": 2, "isUnityClass": true}]}