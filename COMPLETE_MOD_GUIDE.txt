========================================
  COMPLETE SONS OF THE FOREST MOD GUIDE
========================================

🎉 FOUND THE WORKING SOLUTION! 🎉

The original SOTFmenu creator (ImAxel) has made a NEW VERSION
that works with current game versions using RedLoader!

========================================
WHAT WE'RE INSTALLING:
========================================

✅ RedLoader - Modern mod framework for Sons of the Forest
✅ RedManager - Easy mod installer and manager  
✅ Axel's Mod Menu v1.3.7 - The NEW working mod menu

========================================
FEATURES IN AXEL'S MOD MENU v1.3.7:
========================================

🎮 PLAYER FEATURES:
• God Mode & Infinite Health
• Infinite Stamina
• No Hunger/Thirst/Sleep
• Speed Controls & NoClip
• Teleport & Raycast Teleport

🗺️ INTERACTIVE MAP:
• Full teleport map with locations
• Player position display
• Click to teleport anywhere

🛠️ WORLD EDITING:
• World Editor Mode
• Item Spawning (all items)
• Character Spawning (enemies, Kelvin, etc.)
• Season Control (Summer/Winter)
• Time Controls (Day/Night speed)

⚔️ COMBAT & ITEMS:
• Weapon modifications
• Armor equipping
• Achievement unlock/lock
• Build mode enhancements

========================================
INSTALLATION STEPS:
========================================

STEP 1: RUN THE INSTALLER
• Double-click: INSTALL_WORKING_MODS.bat
• This downloads RedManager automatically

STEP 2: USE REDMANAGER
• RedManager will open automatically
• Click "Install RedLoader" in Main tab
• Go to "Browse Mods" tab
• Search "Axel's Mod Menu"
• Click Install

STEP 3: LAUNCH GAME
• Start Sons of the Forest normally
• Load into your game world
• Press F1 to open the mod menu

========================================
MENU KEY: F1
========================================

NOT P, NOT F4 - IT'S F1!

If F1 doesn't work, try:
• F2 or F3
• Check RedManager mod status
• Ensure game is Borderless Window mode

========================================
IMPORTANT LINKS:
========================================

RedManager Download:
https://github.com/ToniMacaroni/RedManager/releases/latest

Axel's Mod Menu Page:
https://sotf-mods.com/mods/imaxel/axel's-mod-menu

Sons of the Forest Mods Site:
https://sotf-mods.com/

RedLoader GitHub:
https://github.com/ToniMacaroni/RedLoader

========================================
WHY THIS WORKS (vs our failed attempts):
========================================

❌ ReiHook v2.0: Uses outdated BepInEx framework
❌ Old SOTFmenu: Discontinued, direct injection method
❌ BepInEx mods: Framework compatibility issues

✅ RedLoader: Modern framework designed for current game
✅ Axel's Mod Menu: Updated by original creator
✅ Active development: Regular updates for new game versions

========================================
TROUBLESHOOTING:
========================================

GAME CRASHES:
• Ensure Borderless Window mode
• Close MSI Afterburner/RTSS
• Verify RedLoader installed correctly

MENU NOT APPEARING:
• Try F1, F2, F3 keys
• Check RedManager for mod status
• Restart game after mod installation

MOD NOT WORKING:
• Update RedLoader in RedManager
• Check for mod updates
• Join Discord: https://discord.gg/sotf

========================================
ALTERNATIVE: CHEAT ENGINE
========================================

If RedLoader doesn't work for you:

1. Download Cheat Engine
2. Search "Sons of the Forest Cheat Table 2024"
3. Check FearlessRevolution.com
4. Most reliable long-term solution

========================================
SUCCESS INDICATORS:
========================================

✅ RedManager opens and shows mods
✅ Game launches without crashing  
✅ F1 opens the mod menu in-game
✅ All features work as expected

This is the CURRENT working solution as of 2024!
The modding community moved from BepInEx to RedLoader.
