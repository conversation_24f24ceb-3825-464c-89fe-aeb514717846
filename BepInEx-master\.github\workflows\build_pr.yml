name: Build PR

on:
    pull_request:
        branches:
            - master

jobs:
    build:
        runs-on: ubuntu-latest
        steps:
            -   uses: actions/checkout@v2.3.5
            -   uses: actions/setup-dotnet@v4
                with:
                    dotnet-version: "8.0.x"
            -   name: Build
                run: |
                    ./build.sh --target Publish
            -   name: Upload Artifacts
                uses: actions/upload-artifact@v4
                with:
                    path: "./bin/dist/*.zip"
                    name: "BepInEx_CI"
