name: Bug Report
description: Report a behaviour that is not desired in BepInEx
labels: [bug]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!
        Please note that this report is for bugs **in BepInEx**.
        
        If you have an issue with a plugin, contact the plugin developer first.
  - type: textarea
    id: basic-description
    attributes:
      label: What happened?
      description: Please describe a bug shortly.
      placeholder: Tell us what you see!
    validations:
      required: true
  - type: textarea
    id: reproduction
    attributes:
      label: Steps to reproduce
      description: If applicable, include the steps to reproduce the issue.
      placeholder: |
        1. In this game...
        2. With this configuration...
        3. Do this...
        4. See error...
    validations:
      required: false
  - type: dropdown
    id: bepin-distro
    attributes:
      label: BepInEx Distribution
      description: What BepInEx build are you using?
      options:
        - Stable from GitHub
        - Bleeding Edge from BepisBuilds
        - 3rd party distribution
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: Log outputs
      description: |
        Please copy and paste any relevant log output.
        You can use **one or multiple** of the following log sources:
        
        - BepInEx Log: located in `BepInEx/LogOutput.log`
        - Preloader error log: located in game folder and named `preloader_<date>.log`
        - Unity Player log: `output_log.txt` or `Player.log` generated by Unity
        - Copy-paste from BepInEx console if enabled
        
        Paste only the logs. They will be automatically reformatted into code blocks.
      render: txt
  - type: textarea
    id: env
    attributes:
      label: Environment
      description: |
        Fill in information about your game and precise BepInEx version  
        Example:
          - **OS**: Ubuntu 20.04
          - **BepInEx**: 5.4.13
          - **Game**: Risk of Rain 2
        
        You can check BepInEx version by checking version of `BepInEx/core/BepInEx.dll` or `BepInEx/core/BepInEx.Core.dll`.
      value: |
          - OS: 
          - BepInEx: 
          - Game: 
      render: markdown
    validations:
      required: true
