<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Il2CppInterop.HarmonySupport</name>
    </assembly>
    <members>
        <member name="M:Il2CppInterop.HarmonySupport.Il2CppDetourMethodPatcher.#ctor(System.Reflection.MethodBase)">
            <summary>
                Constructs a new instance of <see cref="T:MonoMod.RuntimeDetour.NativeDetour" /> method patcher.
            </summary>
            <param name="original"></param>
        </member>
        <member name="M:Il2CppInterop.HarmonySupport.Il2CppDetourMethodPatcher.PrepareOriginal">
            <inheritdoc />
        </member>
        <member name="M:Il2CppInterop.HarmonySupport.Il2CppDetourMethodPatcher.DetourTo(System.Reflection.MethodBase)">
            <inheritdoc />
        </member>
        <member name="M:Il2CppInterop.HarmonySupport.Il2CppDetourMethodPatcher.CopyOriginal">
            <inheritdoc />
        </member>
    </members>
</doc>
