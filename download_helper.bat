@echo off
title SOTFmenu Download Helper
color 0E
echo.
echo ========================================
echo   SOTFmenu Download Helper
echo ========================================
echo.

echo [33mThis script will help you download the SOTFmenu mod.[0m
echo [33mSince I cannot download files automatically, you'll need to:[0m
echo.
echo [36m1. REGISTER/LOGIN:[0m
echo [37m   - Go to UnknownCheats forum[0m
echo [37m   - Create a free account if you don't have one[0m
echo [37m   - Login to your account[0m
echo.
echo [36m2. DOWNLOAD:[0m
echo [37m   - Download SOTFmenu v0.7.7[0m
echo [37m   - Save it to your Downloads folder[0m
echo.
echo [36m3. EXTRACT:[0m
echo [37m   - Extract the zip file[0m
echo [37m   - Find SOTFmenu.dll in the extracted files[0m
echo.
echo [36m4. COPY:[0m
echo [37m   - Copy SOTFmenu.dll to this game directory[0m
echo [37m   - Copy Launch SOTF Mod Menu.exe (optional)[0m
echo.

set /p choice="Press 1 to open download page, 2 to check if files exist, or 3 to exit: "

if "%choice%"=="1" (
    echo.
    echo [32mOpening download page...[0m
    start "" "https://www.unknowncheats.me/forum/downloads.php?do=file&id=42183"
    echo [33mAfter downloading, run this script again and press 2 to verify.[0m
    echo.
    pause
    goto :eof
)

if "%choice%"=="2" (
    echo.
    echo [36mChecking for required files...[0m
    echo.
    
    if exist "SOTFmenu.dll" (
        echo [32m✓ SOTFmenu.dll found![0m
        for %%A in ("SOTFmenu.dll") do echo [37m  Size: %%~zA bytes[0m
        for %%A in ("SOTFmenu.dll") do echo [37m  Date: %%~tA[0m
        set mod_found=1
    ) else (
        echo [31m✗ SOTFmenu.dll not found[0m
        set mod_found=0
    )
    
    if exist "Launch SOTF Mod Menu.exe" (
        echo [32m✓ Launch SOTF Mod Menu.exe found![0m
    ) else (
        echo [33m! Launch SOTF Mod Menu.exe not found (optional)[0m
    )
    
    if exist "%USERPROFILE%\Documents\SOTF\config.ini" (
        echo [32m✓ Configuration file exists[0m
    ) else (
        echo [31m✗ Configuration missing - run install_sotf_menu.bat first[0m
    )
    
    echo.
    if "%mod_found%"=="1" (
        echo [32m🎉 READY TO PLAY![0m
        echo [37mRun launch_with_mod.bat to start the game with mods[0m
        echo.
        set /p launch="Would you like to launch the game now? (y/n): "
        if /i "%launch%"=="y" (
            echo [36mLaunching game with mod support...[0m
            call launch_with_mod.bat
        )
    ) else (
        echo [31m❌ MOD FILES MISSING[0m
        echo [37mPlease download and copy SOTFmenu.dll to this directory[0m
    )
    echo.
    pause
    goto :eof
)

if "%choice%"=="3" (
    echo [37mExiting...[0m
    goto :eof
)

echo [31mInvalid choice. Please run the script again.[0m
pause
