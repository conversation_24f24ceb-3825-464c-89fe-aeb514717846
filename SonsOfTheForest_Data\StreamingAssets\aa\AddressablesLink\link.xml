<linker>
  <assembly fullname="AnimatorEvent, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Ashkatchap.AnimatorEvents.AnimatorEvent" preserve="all" />
    <type fullname="Ashkatchap.AnimatorEvents.EventSMB" preserve="all" />
    <type fullname="Ashkatchap.AnimatorEvents.EventSMB/Action" preserve="nothing" serialized="true" />
    <type fullname="Ashkatchap.AnimatorEvents.EventSMB/Condition" preserve="nothing" serialized="true" />
    <type fullname="Ashkatchap.AnimatorEvents.EventSMB/Entry" preserve="nothing" serialized="true" />
    <type fullname="Ashkatchap.AnimatorEvents.AnimatorEvent/EventElement" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Assembly-CSharp, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Rendering.FireParticle" preserve="all" />
    <type fullname="SimpleBottleFluidController" preserve="all" />
  </assembly>
  <assembly fullname="AstarPathfindingProject, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Pathfinding.NavmeshCut" preserve="all" />
    <type fullname="Pathfinding.GraphMask" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="bolt, Version=0.4.3.4, Culture=neutral, PublicKeyToken=null">
    <type fullname="BoltEntity" preserve="all" />
  </assembly>
  <assembly fullname="BulletBallistics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Ballistics.BallisticMaterial" preserve="all" />
    <type fullname="Ballistics.BallisticObject" preserve="all" />
    <type fullname="Ballistics.Bullet" preserve="all" />
    <type fullname="BulletDebugger" preserve="all" />
    <type fullname="Ballistics.OnImpactEvent" preserve="nothing" serialized="true" />
    <type fullname="Ballistics.OnSurfaceImpactEvent" preserve="nothing" serialized="true" />
    <type fullname="Ballistics.BulletInfo" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Crest, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Crest.RegisterAnimWavesInput" preserve="all" />
  </assembly>
  <assembly fullname="DigitalRuby.ProceduralLightning, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="DigitalRuby.ThunderAndLightning.LightningBoltPrefabScript" preserve="all" />
    <type fullname="DigitalRuby.ThunderAndLightning.LightningCustomTransformDelegate" preserve="nothing" serialized="true" />
    <type fullname="DigitalRuby.ThunderAndLightning.LightningLightParameters" preserve="nothing" serialized="true" />
    <type fullname="DigitalRuby.ThunderAndLightning.RangeOfFloats" preserve="nothing" serialized="true" />
    <type fullname="DigitalRuby.ThunderAndLightning.RangeOfIntegers" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Endnight, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="AnimatorControllerAddressable" preserve="all" />
    <type fullname="Endnight.Animation.AnimationSync" preserve="all" />
    <type fullname="Endnight.Animation.SkinnedMeshAddressable" preserve="all" />
    <type fullname="Endnight.Animation.SkinnedMeshBoneRemapCache" preserve="all" />
    <type fullname="Endnight.Physics.IgnoreCollision" preserve="all" />
    <type fullname="Endnight.Physics.SetCenterOfMass" preserve="all" />
    <type fullname="Endnight.Rendering.AssetReferenceRenderable" preserve="all" />
    <type fullname="Endnight.Rendering.AssetReferenceRenderableCollisionLink" preserve="all" />
    <type fullname="Endnight.Rendering.ExcludeRenderableFrom" preserve="all" />
    <type fullname="Endnight.Rendering.SetRequestedMipmapLevel" preserve="all" />
    <type fullname="Endnight.Utilities.DisableOnAwake" preserve="all" />
    <type fullname="Endnight.Utilities.EnableAfterDelay" preserve="all" />
    <type fullname="Endnight.Utilities.EnableTimedToggle" preserve="all" />
    <type fullname="Endnight.Utilities.ForceObjectLayer" preserve="all" />
    <type fullname="Endnight.Utilities.MouseEventsProxy" preserve="all" />
    <type fullname="Endnight.Utilities.RandomRotation" preserve="all" />
    <type fullname="Endnight.Utilities.SetTargetsActive" preserve="all" />
    <type fullname="Endnight.Utilities.SortChildren" preserve="all" />
    <type fullname="RandomSelector" preserve="all" />
    <type fullname="Endnight.Animation.AnimationSyncData" preserve="nothing" serialized="true" />
    <type fullname="Endnight.Rendering.RequestedMipmapLevelDef" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Endnight.Localization, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Endnight.Localization.LocalizationArray" preserve="all" />
    <type fullname="Endnight.Localization.LocalizationArrayEx" preserve="all" />
  </assembly>
  <assembly fullname="Endnight.PrefabStore, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Endnight.PrefabStore.PrefabReference" preserve="all" />
    <type fullname="Endnight.PrefabStore.PrefabStoreReference" preserve="all" />
    <type fullname="Endnight.PrefabStore.WeakPrefabReference" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="PathCreator, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="PathCreation.Examples.PathFollower" preserve="all" />
    <type fullname="PathCreation.PathCreator" preserve="all" />
    <type fullname="PathCreation.BezierPath" preserve="nothing" serialized="true" />
    <type fullname="PathCreation.PathCreatorData" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="PathologicalGames, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="PathologicalGames.PoolLink" preserve="all" />
  </assembly>
  <assembly fullname="Sons, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="AnimateOverTime" preserve="all" />
    <type fullname="AutoParentConstraint" preserve="all" />
    <type fullname="BloodApplier" preserve="all" />
    <type fullname="Bomb" preserve="all" />
    <type fullname="BreakableObjectBreaker" preserve="all" />
    <type fullname="BuildOutQuickSelectLayoutItems" preserve="all" />
    <type fullname="ChainsawChain" preserve="all" />
    <type fullname="CookingPotController" preserve="all" />
    <type fullname="CoopDynamicPickUp" preserve="all" />
    <type fullname="CoopDynamicPickupWithInstanceModules" preserve="all" />
    <type fullname="DetachParticlesBulletOnImpact" preserve="all" />
    <type fullname="EnvironmentBreaker" preserve="all" />
    <type fullname="Explode" preserve="all" />
    <type fullname="FanSpin" preserve="all" />
    <type fullname="FireSphere" preserve="all" />
    <type fullname="FMOD.FMOD_AnimationEventHandler" preserve="all" />
    <type fullname="FMOD_StudioEventEmitter" preserve="all" />
    <type fullname="HatchBabyTurtle" preserve="all" />
    <type fullname="HatchedBabyTurtleEgg" preserve="all" />
    <type fullname="LaserSight" preserve="all" />
    <type fullname="LightningStrikeDynamicPosition" preserve="all" />
    <type fullname="LightningTargeter" preserve="all" />
    <type fullname="LODGroupRendererAssigner" preserve="all" />
    <type fullname="MergedCollisionPrep" preserve="all" />
    <type fullname="particleController" preserve="all" />
    <type fullname="ParticleScaler" preserve="all" />
    <type fullname="ProjectileImpactObject" preserve="all" />
    <type fullname="ProjectileTrajectoryOffsetData" preserve="all" />
    <type fullname="PulseGameObjects" preserve="all" />
    <type fullname="RebreatherControllerHookups" preserve="all" />
    <type fullname="Sons.Ai.ButterflyLandLocation" preserve="all" />
    <type fullname="Sons.Animation.AnimationAudioEventManager" preserve="all" />
    <type fullname="Sons.Animation.BlendShapes.BlendShapeBoneDriver" preserve="all" />
    <type fullname="Sons.Animation.BlendShapes.BlendShapeManager" preserve="all" />
    <type fullname="Sons.Animation.BlendShapes.PuffyBreathingVfx" preserve="all" />
    <type fullname="Sons.Animation.IkHeldRenderer" preserve="all" />
    <type fullname="Sons.Crafting.CraftingResultLayoutItem" preserve="all" />
    <type fullname="Sons.Crafting.CraftingResultLayoutItemGroup" preserve="all" />
    <type fullname="Sons.Data.PhysicsSfxData" preserve="all" />
    <type fullname="Sons.Gameplay.ArrowUpgradeVisualUpdater" preserve="all" />
    <type fullname="Sons.Gameplay.BigHeadMode" preserve="all" />
    <type fullname="Sons.Gameplay.BreakableObject" preserve="all" />
    <type fullname="Sons.Gameplay.Breeding.RabbitInHutchAnimation" preserve="all" />
    <type fullname="Sons.Gameplay.BurnableFoliage" preserve="all" />
    <type fullname="Sons.Gameplay.ContainerItemSpawner" preserve="all" />
    <type fullname="Sons.Gameplay.ContainerItemSpawnerData" preserve="all" />
    <type fullname="Sons.Gameplay.Cooking.CookingPotIngredientTweener" preserve="all" />
    <type fullname="Sons.Gameplay.Cooking.CookingPotIngredientVisual" preserve="all" />
    <type fullname="Sons.Gameplay.Cooking.CookingPotLid" preserve="all" />
    <type fullname="Sons.Gameplay.Cooking.CookingPotStewVisuals" preserve="all" />
    <type fullname="Sons.Gameplay.DroppedBagUiMarker" preserve="all" />
    <type fullname="Sons.Gameplay.DroppedInventoryItemsPickup" preserve="all" />
    <type fullname="Sons.Gameplay.EdiblePickUp" preserve="all" />
    <type fullname="Sons.Gameplay.ExplosiveArrowController" preserve="all" />
    <type fullname="Sons.Gameplay.FireArrowController" preserve="all" />
    <type fullname="Sons.Gameplay.GenericInteraction" preserve="all" />
    <type fullname="Sons.Gameplay.GiveItemsOnHit" preserve="all" />
    <type fullname="Sons.Gameplay.GPS.GPSLocator" preserve="all" />
    <type fullname="Sons.Gameplay.GPS.GPSLocatorIcons" preserve="all" />
    <type fullname="Sons.Gameplay.GPS.GpsLocatorVisualsHookup" preserve="all" />
    <type fullname="Sons.Gameplay.GPS.GPSTrackerSystem" preserve="all" />
    <type fullname="Sons.Gameplay.ItemAnchorLocalOffsetData" preserve="all" />
    <type fullname="Sons.Gameplay.ItemPlatingRendererUpdater" preserve="all" />
    <type fullname="Sons.Gameplay.KillSpider" preserve="all" />
    <type fullname="Sons.Gameplay.KnightVItemController" preserve="all" />
    <type fullname="Sons.Gameplay.LootPouchItemRevealController" preserve="all" />
    <type fullname="Sons.Gameplay.MeleeWeapon" preserve="all" />
    <type fullname="Sons.Gameplay.MuzzleFlash" preserve="all" />
    <type fullname="Sons.Gameplay.ObjectPhysicsInteractionSfx" preserve="all" />
    <type fullname="Sons.Gameplay.PickUp" preserve="all" />
    <type fullname="Sons.Gameplay.PlatedArmourWornController" preserve="all" />
    <type fullname="Sons.Gameplay.PoisonArrowController" preserve="all" />
    <type fullname="Sons.Gameplay.ProjectileNetworkProxy" preserve="all" />
    <type fullname="Sons.Gameplay.SeatTrigger" preserve="all" />
    <type fullname="Sons.Gameplay.ShockArrowController" preserve="all" />
    <type fullname="Sons.Gameplay.SpawnedObjectTag" preserve="all" />
    <type fullname="Sons.Gameplay.StuckSlugAgitationTrigger" preserve="all" />
    <type fullname="Sons.Gameplay.ToggleOnLayerTriggerCheck" preserve="all" />
    <type fullname="Sons.Gameplay.TriggerSluggieKilledEvent" preserve="all" />
    <type fullname="Sons.Gameplay.ZipLines.ActorZiplineInteraction" preserve="all" />
    <type fullname="Sons.Inventory.InventoryBagController" preserve="all" />
    <type fullname="Sons.Inventory.PerishableItemUniqueMaterialSwap" preserve="all" />
    <type fullname="Sons.Inventory.QuickSelectLayoutItem" preserve="all" />
    <type fullname="Sons.Inventory.QuickSlotHookPoint" preserve="all" />
    <type fullname="Sons.Inventory.VisualVariantController" preserve="all" />
    <type fullname="Sons.Item.PlasmaLighterController" preserve="all" />
    <type fullname="Sons.Items.ArtifactInteriorHookup" preserve="all" />
    <type fullname="Sons.Items.ArtifactPowerLevelIndicator" preserve="all" />
    <type fullname="Sons.Items.BloodOnItem" preserve="all" />
    <type fullname="Sons.Utilities.Unparent" preserve="all" />
    <type fullname="Sons.Utils.DistanceActivationEmitter" preserve="all" />
    <type fullname="Sons.Utils.GenericDistanceActivator" preserve="all" />
    <type fullname="Sons.Weapon.BowWeaponController" preserve="all" />
    <type fullname="Sons.Weapon.CompactPistolWeaponController" preserve="all" />
    <type fullname="Sons.Weapon.CrossbowWeaponController" preserve="all" />
    <type fullname="Sons.Weapon.CrossController" preserve="all" />
    <type fullname="Sons.Weapon.EffectFadeController" preserve="all" />
    <type fullname="Sons.Weapon.EmailPageController" preserve="all" />
    <type fullname="Sons.Weapon.GpsLocatorController" preserve="all" />
    <type fullname="Sons.Weapon.MaskAnimatorController" preserve="all" />
    <type fullname="Sons.Weapon.RangedWeapon" preserve="all" />
    <type fullname="Sons.Weapon.RevolverWeaponController" preserve="all" />
    <type fullname="Sons.Weapon.RifleAnimatorController" preserve="all" />
    <type fullname="Sons.Weapon.RopeGunController" preserve="all" />
    <type fullname="Sons.Weapon.ShotgunWeaponController" preserve="all" />
    <type fullname="Sons.Weapon.SlingshotWeaponController" preserve="all" />
    <type fullname="Sons.Weapon.StunGunWeaponController" preserve="all" />
    <type fullname="Sons.Weapon.TutorialPageController" preserve="all" />
    <type fullname="Sons.Wearable.Clothing.ClothingPieceSlotRenderableLocator" preserve="all" />
    <type fullname="Sons.World.ObjectRainInteraction" preserve="all" />
    <type fullname="TheForest.Items.Inventory.HeldItemIdentifier" preserve="all" />
    <type fullname="TheForest.UI.VirtualCursorSnapAnchor" preserve="all" />
    <type fullname="TheForest.World.EnvironmentWetnessReceiverProxy" preserve="all" />
    <type fullname="TheForest.World.LightFadeInAndOut" preserve="all" />
    <type fullname="TireDecalSpawner" preserve="all" />
    <type fullname="TreeCutSetPivots" preserve="all" />
    <type fullname="VehicleTrailSpawner" preserve="all" />
    <type fullname="weaponInfo" preserve="all" />
    <type fullname="Sons.Gameplay.BreakableObject/SpawnDefinition" preserve="nothing" serialized="true" />
    <type fullname="Sons.Weapon.RangedWeapon/Ammo" preserve="nothing" serialized="true" />
    <type fullname="Sons.Gameplay.ItemAnchorLocalOffsetData/ItemAnchorLocalOffsetDataElement" preserve="nothing" serialized="true" />
    <type fullname="TheForest.Items.Special.LightFireReceiver" preserve="nothing" serialized="true" />
    <type fullname="Sons.Animation.AnimationAudioEventManager/AnimationAudioEvent" preserve="nothing" serialized="true" />
    <type fullname="Sons.Inventory.PerishableItemUniqueMaterialSwap/StateMaterial" preserve="nothing" serialized="true" />
    <type fullname="Sons.Gameplay.LootPouchItemRevealController/ItemLocator" preserve="nothing" serialized="true" />
    <type fullname="Sons.Gameplay.GPS.GPSLocatorIcons/IconData" preserve="nothing" serialized="true" />
    <type fullname="Sons.Inventory.VisualVariantController/VariantData" preserve="nothing" serialized="true" />
    <type fullname="Sons.Inventory.VisualVariantController/VisualData" preserve="nothing" serialized="true" />
    <type fullname="Sons.Gameplay.ContainerItemSpawnerData/PickupItemAndSpawnWeight" preserve="nothing" serialized="true" />
    <type fullname="Sons.Gameplay.EdiblePickUp/PlayerStatConditionList" preserve="nothing" serialized="true" />
    <type fullname="Sons.Weapon.EffectFadeController/LightFlickerFader" preserve="nothing" serialized="true" />
    <type fullname="Sons.Weapon.EffectFadeController/ParticleScalerFader" preserve="nothing" serialized="true" />
    <type fullname="Sons.Weapon.EffectFadeController/RendererFader" preserve="nothing" serialized="true" />
    <type fullname="Sons.Animation.BlendShapes.BlendShapeBoneDriver/ShapeDriver" preserve="nothing" serialized="true" />
    <type fullname="Sons.Animation.BlendShapes.PuffyBlendShapeAnimation" preserve="nothing" serialized="true" />
    <type fullname="RendererList" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Sons.Ai.Utilities, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Sons.Ai.VailCollisionTags" preserve="all" />
  </assembly>
  <assembly fullname="Sons.Ai.Vail, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Sons.Ai.Vail.ClimbOutEffect" preserve="all" />
    <type fullname="Sons.Ai.Vail.DismemberMaterialRenderer" preserve="all" />
    <type fullname="Sons.Ai.Vail.Inventory.VailPickup" preserve="all" />
    <type fullname="Sons.Ai.Vail.StimuliTypes.BloodGroundStimuli" preserve="all" />
    <type fullname="Sons.Ai.Vail.StimuliTypes.BloodTroughStimuli" preserve="all" />
    <type fullname="Sons.Ai.Vail.StimuliTypes.ExplosionEvent" preserve="all" />
    <type fullname="Sons.Ai.Vail.StimuliTypes.HeavyThroneStimuli" preserve="all" />
    <type fullname="Sons.Ai.Vail.StimuliTypes.LandingSpotStimuli" preserve="all" />
    <type fullname="Sons.Ai.Vail.StimuliTypes.LightStimuli" preserve="all" />
    <type fullname="Sons.Ai.Vail.StimuliTypes.MaskStimuli" preserve="all" />
    <type fullname="Sons.Ai.Vail.StimuliTypes.MeatStimuli" preserve="all" />
    <type fullname="Sons.Ai.Vail.StimuliTypes.MediumNoiseStimuli" preserve="all" />
    <type fullname="Sons.Ai.Vail.StimuliTypes.StickPickupStimuli" preserve="all" />
    <type fullname="Sons.Ai.Vail.StimuliTypes.ZiplineStimuli" preserve="all" />
    <type fullname="Sons.Ai.Vail.StimuliTypes.ConsumableData" preserve="nothing" serialized="true" />
    <type fullname="Sons.Ai.Vail.MonoBehaviourStimuli/ReservableLocation" preserve="nothing" serialized="true" />
    <type fullname="Sons.Ai.Vail.StimuliTypes.EventDescription" preserve="nothing" serialized="true" />
    <type fullname="Sons.Ai.Vail.StimuliTypes.StatAdjustmentsByClass" preserve="nothing" serialized="true" />
    <type fullname="Sons.Ai.Vail.DismemberMaterialRenderer/DismemberModel" preserve="nothing" serialized="true" />
    <type fullname="Sons.Ai.Vail.StimuliTypes.StatAdjustmentsByClass/MemoryFactor" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Sons.Atmosphere, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Sons.Atmosphere.ParticleDistanceFixer" preserve="all" />
    <type fullname="Sons.Atmosphere.TemperatureModifierVolume" preserve="all" />
  </assembly>
  <assembly fullname="Sons.Cinematics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="CinematicMuzzleFlashController" preserve="all" />
  </assembly>
  <assembly fullname="Sons.Electricity, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Sons.Electricity.BatteryIndicator" preserve="all" />
  </assembly>
  <assembly fullname="Sons.Environment, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Sons.Environment.Digging.TerrainDig.TerrainDigFade" preserve="all" />
    <type fullname="Sons.Environment.Digging.TerrainDig.TerrainDigNode" preserve="all" />
    <type fullname="Sons.Environment.Digging.TerrainDig.TerrainDigNodeConnector" preserve="all" />
    <type fullname="Sons.Environment.Digging.TerrainDig.TerrainDigThroughArea" preserve="all" />
  </assembly>
  <assembly fullname="Sons.FMOD, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="SonsFMODEventEmitter" preserve="all" />
  </assembly>
  <assembly fullname="Sons.Gui, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Sons.Gui.Input.LinkUiElement" preserve="all" />
  </assembly>
  <assembly fullname="Sons.Item, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="AmmoProperties" preserve="all" />
    <type fullname="Sons.Items.Core.ItemRenderable" preserve="all" />
    <type fullname="Sons.Items.Core.ItemRenderableTag" preserve="all" />
    <type fullname="Sons.Utils.RandomRange" preserve="nothing" serialized="true" />
    <type fullname="ProjectileInfo" preserve="nothing" serialized="true" />
    <type fullname="Sons.Items.Core.ItemPrefabs" preserve="nothing" serialized="true" />
    <type fullname="ItemAndRange" preserve="nothing" serialized="true" />
    <type fullname="Sons.Items.Core.MeleeWeaponData" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Sons.Multiplayer, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Sons.Multiplayer.BoltEntityDynamicSmooth" preserve="all" />
  </assembly>
  <assembly fullname="Sons.Physics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Sons.Gameplay.Swimming.FloatController" preserve="all" />
    <type fullname="Sons.Physics.AddExplosionForce" preserve="all" />
    <type fullname="Sons.Physics.ExplosionEffect" preserve="all" />
    <type fullname="Sons.Physics.SetMaxDepenetrationVelocity" preserve="all" />
    <type fullname="Sons.Physics.WaterLevelSensor" preserve="all" />
    <type fullname="SpiritLevel" preserve="all" />
    <type fullname="Sons.Gameplay.Swimming.FloatController/FloatLocation" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Sons.Rocks, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Sons.Rocks.RockDestructionChunk" preserve="all" />
    <type fullname="Sons.Rocks.RockDestructionController" preserve="all" />
    <type fullname="Sons.Rocks.RockDestructionFracture" preserve="all" />
    <type fullname="Sons.Rocks.RockChunkData" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Sons.Subtitles, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="AnimatorStateSubtitlesProvider" preserve="all" />
    <type fullname="SubtitlesSequence" preserve="all" />
    <type fullname="SubtitleEntry" preserve="nothing" serialized="true" />
    <type fullname="SubtitlesPlayerInterfaceReference" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Sons.Utilities, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Sons.Utilities.DestroyAfter" preserve="all" />
  </assembly>
  <assembly fullname="Sons.VFX, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="AnimationScripted" preserve="all" />
    <type fullname="AnimationScriptedSettings" preserve="all" />
    <type fullname="Assemblies.Sons.VFX.BeachWaveVFX" preserve="all" />
    <type fullname="BeachWaveVFXSettings" preserve="all" />
    <type fullname="BloodImpact" preserve="all" />
    <type fullname="BurnableObjectsActivator" preserve="all" />
    <type fullname="EnableChildrenDelay" preserve="all" />
    <type fullname="Sons.Lighting.LightFlicker" preserve="all" />
    <type fullname="Sons.VFX.BeachWaveVFXTimePass" preserve="all" />
    <type fullname="Sons.VFX.EnableChildren" preserve="all" />
    <type fullname="Sons.VFX.TriggerActivate" preserve="all" />
    <type fullname="Sons.VFX.TriggerRigidbodyEffect" preserve="all" />
  </assembly>
  <assembly fullname="Sons.Weapon, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Sons.Weapon.DamageController" preserve="all" />
    <type fullname="Sons.Weapon.DamageNode" preserve="all" />
    <type fullname="Sons.Weapon.DamageNodeMap" preserve="all" />
    <type fullname="Sons.Weapon.PhysicalImpactReceiver" preserve="all" />
    <type fullname="Sons.Weapon.ProjectileTransformConstraint" preserve="all" />
    <type fullname="Sons.Weapon.WeaponMods" preserve="all" />
    <type fullname="Sons.Weapon.WeaponMods/ModLink" preserve="nothing" serialized="true" />
    <type fullname="Sons.Weapon.DamageNodeMap/DamageNodeId" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="TheForest.Utils, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="TheForest.Utils.DisableAfterDelay" preserve="all" />
  </assembly>
  <assembly fullname="Unity.Addressables, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.AddressableAssets.Addressables" preserve="all" />
    <type fullname="UnityEngine.AddressableAssets.AssetReference" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.Localization, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Localization.Locale" preserve="all" />
    <type fullname="UnityEngine.Localization.Tables.SharedTableData" preserve="all" />
    <type fullname="UnityEngine.Localization.Tables.StringTable" preserve="all" />
    <type fullname="UnityEngine.Localization.LocaleIdentifier" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.Metadata.MetadataCollection" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.Metadata.SmartFormatTag" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.Tables.TableEntryData" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.Metadata.FallbackLocale" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.Metadata.Comment" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.Tables.DistributedUIDGenerator" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.Tables.SharedTableData/SharedTableEntry" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.LocalizedString" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.Tables.TableEntryReference" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Localization.Tables.TableReference" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.RenderPipelines.HighDefinition.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEditor.Rendering.HighDefinition.MaterialExternalReferences" preserve="all" />
    <type fullname="UnityEngine.Rendering.AdditionalShadowData" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.DecalProjector" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.DiffusionProfileSettings" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.HDAdditionalLightData" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.HDAdditionalReflectionData" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.DiffusionProfile" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.BoolScalableSettingValue" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.IntScalableSettingValue" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CameraSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CameraSettings/BufferClearing" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CameraSettings/Culling" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CameraSettings/Frustum" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CameraSettings/Volumes" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CameraSettingsOverride" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CubeReflectionResolution" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.FrameSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.FrameSettingsOverrideMask" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.HDProbe/RenderData" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.InfluenceVolume" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ObsoleteCaptureSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ObsoleteFrameSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ObsoleteLightLoopSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.PlanarReflectionAtlasResolution" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ProbeSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ProbeSettings/CubeReflectionResolutionScalableSettingValue" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ProbeSettings/Frustum" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ProbeSettings/Lighting" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ProbeSettings/PlanarReflectionAtlasResolutionScalableSettingValue" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ProbeSettings/ProxySettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ProbeSettingsOverride" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ProxyVolume" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.InstanceProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.LegacyResourcesProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.SceneProvider" preserve="all" />
  </assembly>
  <assembly fullname="Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="TMPro.TextMeshProUGUI" preserve="all" />
    <type fullname="TMPro.TMP_FontAsset" preserve="all" />
    <type fullname="TMPro.VertexGradient" preserve="nothing" serialized="true" />
    <type fullname="TMPro.FaceInfo_Legacy" preserve="nothing" serialized="true" />
    <type fullname="TMPro.FontAssetCreationSettings" preserve="nothing" serialized="true" />
    <type fullname="TMPro.KerningTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.LigatureSubstitutionRecord" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontFeatureTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontWeightPair" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Character" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.AnimationModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Animation" preserve="all" />
    <type fullname="UnityEngine.AnimationClip" preserve="all" />
    <type fullname="UnityEngine.Animations.AimConstraint" preserve="all" />
    <type fullname="UnityEngine.Animations.ParentConstraint" preserve="all" />
    <type fullname="UnityEngine.Animations.PositionConstraint" preserve="all" />
    <type fullname="UnityEngine.Animations.RotationConstraint" preserve="all" />
    <type fullname="UnityEngine.Animator" preserve="all" />
    <type fullname="UnityEngine.AnimatorOverrideController" preserve="all" />
    <type fullname="UnityEngine.Avatar" preserve="all" />
    <type fullname="UnityEngine.RuntimeAnimatorController" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Cubemap" preserve="all" />
    <type fullname="UnityEngine.GameObject" preserve="all" />
    <type fullname="UnityEngine.Light" preserve="all" />
    <type fullname="UnityEngine.LightProbeProxyVolume" preserve="all" />
    <type fullname="UnityEngine.LODGroup" preserve="all" />
    <type fullname="UnityEngine.Material" preserve="all" />
    <type fullname="UnityEngine.Mesh" preserve="all" />
    <type fullname="UnityEngine.MeshFilter" preserve="all" />
    <type fullname="UnityEngine.MeshRenderer" preserve="all" />
    <type fullname="UnityEngine.Object" preserve="all" />
    <type fullname="UnityEngine.RectTransform" preserve="all" />
    <type fullname="UnityEngine.ReflectionProbe" preserve="all" />
    <type fullname="UnityEngine.Shader" preserve="all" />
    <type fullname="UnityEngine.SkinnedMeshRenderer" preserve="all" />
    <type fullname="UnityEngine.Sprite" preserve="all" />
    <type fullname="UnityEngine.Texture2D" preserve="all" />
    <type fullname="UnityEngine.Texture2DArray" preserve="all" />
    <type fullname="UnityEngine.Texture3D" preserve="all" />
    <type fullname="UnityEngine.TrailRenderer" preserve="all" />
    <type fullname="UnityEngine.Transform" preserve="all" />
    <type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`1[Ballistics.SurfaceImpactInfo]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`1[Sons.Gameplay.PickUp]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.ArgumentCache" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.PersistentListenerMode" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`2[FMOD.Studio.EventInstance,System.String]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`2[Sons.Weapon.DamageNode,IImpactData]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`1[DigitalRuby.ThunderAndLightning.LightningBoltPrefabScriptBase]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`1[UnityEngine.Vector3]" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent`1[AnimatorControllerAddressable]" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.ParticleSystemModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.ParticleSystem" preserve="all" />
    <type fullname="UnityEngine.ParticleSystemForceField" preserve="all" />
    <type fullname="UnityEngine.ParticleSystemRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.PhysicsModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.BoxCollider" preserve="all" />
    <type fullname="UnityEngine.CapsuleCollider" preserve="all" />
    <type fullname="UnityEngine.MeshCollider" preserve="all" />
    <type fullname="UnityEngine.PhysicMaterial" preserve="all" />
    <type fullname="UnityEngine.Rigidbody" preserve="all" />
    <type fullname="UnityEngine.SphereCollider" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.SpriteMaskModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.SpriteMask" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TextRenderingModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Font" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.UI.CanvasScaler" preserve="all" />
    <type fullname="UnityEngine.UI.GraphicRaycaster" preserve="all" />
    <type fullname="UnityEngine.UI.Image" preserve="all" />
    <type fullname="UnityEngine.UI.Mask" preserve="all" />
    <type fullname="UnityEngine.UI.RawImage" preserve="all" />
    <type fullname="UnityEngine.UI.RectMask2D" preserve="all" />
    <type fullname="UnityEngine.UI.Shadow" preserve="all" />
    <type fullname="UnityEngine.UI.Text" preserve="all" />
    <type fullname="UnityEngine.UI.FontData" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.UIModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Canvas" preserve="all" />
    <type fullname="UnityEngine.CanvasRenderer" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.VideoModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Video.VideoPlayer" preserve="all" />
  </assembly>
  <assembly fullname="Upgen.Lighting, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UL_FastGI" preserve="all" />
    <type fullname="UL_FastLight" preserve="all" />
  </assembly>
  <assembly fullname="VolumetricBloodFX, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="BFX_BloodSettings" preserve="all" />
    <type fullname="BFX_ManualAnimationUpdate" preserve="all" />
  </assembly>
  <assembly fullname="Sons.FieldOfView">
    <type fullname="Sons.FieldOfView.FovManager/FieldOfViewChangeSettings" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Sons.Gameplay.StatusEffects">
    <type fullname="Sons.Gameplay.StatusEffect" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Sons.StatSystem">
    <type fullname="Sons.StatSystem.AffectionStat" preserve="nothing" serialized="true" />
    <type fullname="Sons.StatSystem.AngerStat" preserve="nothing" serialized="true" />
    <type fullname="Sons.StatSystem.FearStat" preserve="nothing" serialized="true" />
    <type fullname="Sons.StatSystem.FullnessStat" preserve="nothing" serialized="true" />
    <type fullname="Sons.StatSystem.StatAdjustment" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.RenderPipelines.Core.Runtime">
    <type fullname="UnityEngine.Rendering.BitArray128" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.TextCoreFontEngineModule">
    <type fullname="UnityEngine.TextCore.FaceInfo" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.Glyph" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphMetrics" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphRect" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphPairAdjustmentRecord" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.LowLevel.GlyphValueRecord" preserve="nothing" serialized="true" />
  </assembly>
</linker>