@echo off
title ReiHook Installation Assistant
color 0A
echo.
echo ========================================
echo   ReiHook Installation Assistant
echo ========================================
echo.

echo [36mSetting up ReiHook mod for Sons of the Forest...[0m
echo.

REM Check if ReiHook file exists
if exist "ReiHook for Sons of the Forest v2.0_[unknowncheats.me]_.dll" (
    echo [32m✓ ReiHook mod file found[0m
) else (
    echo [31m✗ ReiHook mod file not found![0m
    echo Please make sure the ReiHook DLL is in this directory.
    pause
    exit /b 1
)

REM Check if game exists
if not exist "SonsOfTheForest.exe" (
    echo [31m✗ Sons of the Forest not found![0m
    echo Please run this from the game directory.
    pause
    exit /b 1
) else (
    echo [32m✓ Game executable found[0m
)

echo.
echo [36mReiHook requires BepInEx framework to work.[0m
echo [33mBepInEx is a Unity modding framework that provides stable mod injection.[0m
echo.

REM Check if BepInEx already exists
if exist "BepInEx" (
    echo [32m✓ BepInEx folder found[0m
    if exist "BepInEx\plugins" (
        echo [32m✓ BepInEx plugins folder exists[0m
    ) else (
        echo [33m! Creating BepInEx plugins folder...[0m
        mkdir "BepInEx\plugins"
    )
) else (
    echo [31m✗ BepInEx not installed[0m
    echo.
    echo [36mBepInEx Installation Required:[0m
    echo [37m1. Download BepInEx 6.0+ from: https://github.com/BepInEx/BepInEx/releases[0m
    echo [37m2. Look for "BepInEx_UnityIL2CPP_x64" version[0m
    echo [37m3. Extract to this game directory[0m
    echo [37m4. Run the game once to initialize BepInEx[0m
    echo [37m5. Then run this installer again[0m
    echo.
    
    set /p download="Open BepInEx download page? (y/n): "
    if /i "%download%"=="y" (
        start "" "https://github.com/BepInEx/BepInEx/releases"
        echo [32mDownload page opened in browser[0m
    )
    
    echo.
    echo [33mAfter installing BepInEx, run this script again.[0m
    pause
    exit /b 0
)

echo.
echo [36mInstalling ReiHook mod...[0m

REM Rename and copy ReiHook to plugins folder
if exist "BepInEx\plugins\ReiHook.dll" (
    echo [33m! ReiHook already installed, backing up...[0m
    ren "BepInEx\plugins\ReiHook.dll" "ReiHook_backup.dll"
)

copy "ReiHook for Sons of the Forest v2.0_[unknowncheats.me]_.dll" "BepInEx\plugins\ReiHook.dll" >nul
if %errorlevel% == 0 (
    echo [32m✓ ReiHook installed successfully![0m
) else (
    echo [31m✗ Failed to install ReiHook[0m
    pause
    exit /b 1
)

echo.
echo [32m🎉 ReiHook Installation Complete![0m
echo.
echo [36mIMPORTANT USAGE INSTRUCTIONS:[0m
echo [37m• Launch the game normally (through Steam or directly)[0m
echo [37m• Wait for the game to fully load[0m
echo [37m• Press F4 to open the ReiHook menu (NOT P!)[0m
echo [37m• Set game to Borderless Window mode for best results[0m
echo.
echo [36mReiHook Features:[0m
echo [37m• Menu Key: F4[0m
echo [37m• Unlimited Health, Stamina, Ammo[0m
echo [37m• ESP (Enemy/Animal/Item detection)[0m
echo [37m• 2D Radar[0m
echo [37m• Item/Mutant/Animal Spawners[0m
echo [37m• Teleportation features[0m
echo [37m• World Editor[0m
echo.

set /p launch="Launch the game now to test ReiHook? (y/n): "
if /i "%launch%"=="y" (
    echo.
    echo [36mLaunching Sons of the Forest...[0m
    echo [33mRemember: Press F4 to open the mod menu![0m
    start "" "SonsOfTheForest.exe"
    echo.
    echo [32mGame launched! Press F4 in-game to access ReiHook menu.[0m
) else (
    echo.
    echo [37mSetup complete! Launch the game and press F4 to use ReiHook.[0m
)

echo.
pause
