﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <Description>BepInEx Core library</Description>
        <TargetFrameworks>net35;netstandard2.0</TargetFrameworks>
        <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
        <!-- Force nuget assembly output on netstandard2.0 -->
        <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.NETFramework.ReferenceAssemblies" Version="1.0.3" PrivateAssets="All"/>
        <PackageReference Include="HarmonyX" Version="2.10.2" />
        <PackageReference Include="SemanticVersioning" Version="2.0.2"/>
        <PackageReference Include="MonoMod.Utils" Version="*********"/>
    </ItemGroup>
    <ItemGroup>
        <Compile Remove="Contract\IPlugin.cs"/>
    </ItemGroup>

    <!-- CopyLocalLockFileAssemblies causes to also output shared assemblies: https://github.com/NuGet/Home/issues/4837#issuecomment-354536302 -->
    <!-- Since all core assemblies usually follow naming of System.*, we just delete them for now -->
    <!-- Also delete deps.json as they are not used by mono -->
    <Target Name="DeleteSys" AfterTargets="Build">
        <ItemGroup>
            <FilesToDelete Include="$(OutputPath)System.*.dll"/>
            <FilesToDelete Include="$(OutputPath)*.deps.json"/>
        </ItemGroup>
        <Delete Files="@(FilesToDelete)"/>
    </Target>
</Project>
