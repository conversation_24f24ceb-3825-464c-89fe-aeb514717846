@echo off
title Sons of the Forest - ReiHook Launcher
color 0A
echo.
echo ========================================
echo   Sons of the Forest - ReiHook Launcher
echo ========================================
echo.

echo [36mChecking ReiHook installation...[0m
echo.

REM Check BepInEx
if not exist "BepInEx" (
    echo [31m✗ BepInEx not installed![0m
    echo [33mReiHook requires BepInEx framework.[0m
    echo.
    set /p install_bepinex="Install BepInEx now? (y/n): "
    if /i "%install_bepinex%"=="y" (
        call INSTALL_BEPINEX.bat
        exit /b 0
    ) else (
        echo [37mCannot proceed without BepInEx.[0m
        pause
        exit /b 1
    )
) else (
    echo [32m✓ BepInEx framework found[0m
)

REM Check ReiHook
if not exist "BepInEx\plugins\ReiHook.dll" (
    echo [31m✗ ReiHook mod not installed![0m
    echo.
    if exist "ReiHook for Sons of the Forest v2.0_[unknowncheats.me]_.dll" (
        echo [33mReiHook file found but not installed.[0m
        set /p install_reihook="Install ReiHook now? (y/n): "
        if /i "%install_reihook%"=="y" (
            call INSTALL_REIHOOK.bat
            exit /b 0
        )
    ) else (
        echo [31mReiHook mod file not found![0m
        echo [37mPlease download ReiHook from UnknownCheats.[0m
    )
    pause
    exit /b 1
) else (
    echo [32m✓ ReiHook mod installed[0m
)

REM Check game
if not exist "SonsOfTheForest.exe" (
    echo [31m✗ Game executable not found![0m
    pause
    exit /b 1
) else (
    echo [32m✓ Game executable found[0m
)

echo.
echo [32m🎮 Ready to launch with ReiHook![0m
echo.
echo [36mReiHook Features Available:[0m
echo [37m• Menu Key: F4 (NOT P!)[0m
echo [37m• Player: Unlimited Health, Stamina, Strength[0m
echo [37m• Survival: No Hunger, Thirst, Tiredness, Cold[0m
echo [37m• Combat: Unlimited Ammo, Rapid Fire[0m
echo [37m• ESP: Enemies, Animals, Items, Containers[0m
echo [37m• 2D Radar with distance indicators[0m
echo [37m• Spawners: Items, Mutants, Animals[0m
echo [37m• Teleportation to caves and bunkers[0m
echo [37m• World Editor features[0m
echo.

echo [36mIMPORTANT REMINDERS:[0m
echo [37m• Press F4 to open the mod menu (F4, not P!)[0m
echo [37m• Set game to Borderless Window mode[0m
echo [37m• Wait for game to fully load before using F4[0m
echo [37m• Close overlay programs if you have issues[0m
echo.

set /p launch="Launch Sons of the Forest with ReiHook? (y/n): "
if /i not "%launch%"=="y" (
    echo [37mLaunch cancelled.[0m
    pause
    exit /b 0
)

echo.
echo [36mLaunching Sons of the Forest with ReiHook mod...[0m
echo.

REM Start the game
start "" "SonsOfTheForest.exe"

echo [32m✓ Game launched successfully![0m
echo.
echo [36mReiHook Usage Instructions:[0m
echo [37m1. Wait for the game to fully load[0m
echo [37m2. Start or load a game[0m
echo [37m3. Press F4 to open the ReiHook menu[0m
echo [37m4. Navigate with mouse and enable features[0m
echo.
echo [33mTroubleshooting:[0m
echo [37m• If F4 doesn't work, make sure game is in Borderless Window[0m
echo [37m• Check BepInEx console for any error messages[0m
echo [37m• Try running as administrator if needed[0m
echo [37m• Make sure no overlay programs are interfering[0m
echo.
echo [32mEnjoy your enhanced Sons of the Forest experience![0m
echo.
pause
