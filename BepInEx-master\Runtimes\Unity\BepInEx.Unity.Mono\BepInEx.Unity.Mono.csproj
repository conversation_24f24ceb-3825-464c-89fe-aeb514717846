﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <Description>BepInEx support library for Mono Unity games</Description>
        <TargetFrameworks>net35;netstandard2.0</TargetFrameworks>
        <AppendTargetFrameworkToOutputPath>true</AppendTargetFrameworkToOutputPath>
    </PropertyGroup>
    <PropertyGroup Condition="'$(TargetFramework)' == 'net35'">
        <!-- Output only net35 version to distribution; netstadnard is only used for generating NuGet package -->
        <OutputPath>$(BuildDir)/Unity.Mono</OutputPath>
        <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    </PropertyGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\..\BepInEx.Core\BepInEx.Core.csproj"/>
        <ProjectReference Include="..\..\..\BepInEx.Preloader.Core\BepInEx.Preloader.Core.csproj" PrivateAssets="All"/>
        <ProjectReference Include="..\BepInEx.Unity.Mono.Preloader\BepInEx.Unity.Mono.Preloader.csproj" PrivateAssets="All"/>
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.NETFramework.ReferenceAssemblies" Version="1.0.3" PrivateAssets="All"/>
        <PackageReference Include="HarmonyX" Version="2.10.2" />
        <PackageReference Include="UnityEngine" Version="5.6.1" IncludeAssets="compile"/>
        <PackageReference Include="Unity.InputSystem" version="1.5.0" IncludeAssets="compile"/>
    </ItemGroup>
</Project>
