<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <Description>BepInEx support library for Il2Cpp games</Description>
        <TargetFramework>net6.0</TargetFramework>
        <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
        <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
        <OutputPath>$(BuildDir)/Unity.IL2CPP</OutputPath>
        <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    </PropertyGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\..\BepInEx.Core\BepInEx.Core.csproj"/>
        <ProjectReference Include="..\..\..\BepInEx.Preloader.Core\BepInEx.Preloader.Core.csproj" PrivateAssets="All"/>
        <ProjectReference Include="..\BepInEx.Unity.Common\BepInEx.Unity.Common.csproj"/>
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="HarmonyX" Version="2.10.2" />
        <PackageReference Include="Iced" Version="1.21.0" />
        <PackageReference Include="Il2CppInterop.Generator" Version="1.5.0-ci.620"/>
        <PackageReference Include="Il2CppInterop.HarmonySupport" Version="1.5.0-ci.620"/>
        <PackageReference Include="Il2CppInterop.ReferenceLibs" Version="1.0.0" IncludeAssets="compile" PrivateAssets="all"/>
        <PackageReference Include="Il2CppInterop.Runtime" Version="1.5.0-ci.620"/>
        <PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" IncludeAssets="compile" PrivateAssets="all"/>
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="6.0.1" IncludeAssets="compile" PrivateAssets="all"/>
        <PackageReference Include="MonoMod.RuntimeDetour" Version="22.7.31.1"/>
        <PackageReference Include="Samboy063.Cpp2IL.Core" Version="2022.1.0-pre-release.19"/>
    </ItemGroup>

    <!-- CopyLocalLockFileAssemblies causes to also output shared assemblies: https://github.com/NuGet/Home/issues/4837#issuecomment-354536302 -->
    <!-- Since all core assemblies usually follow naming of System.*, we just delete them for now -->
    <!-- Also delete deps.json as they are not used by embedded CLR -->
    <Target Name="DeleteSys" AfterTargets="Build">
        <ItemGroup>
            <FilesToDelete Include="$(OutputPath)System.*.dll"/>
            <FilesToDelete Include="$(OutputPath)*.deps.json"/>
        </ItemGroup>
        <Delete Files="@(FilesToDelete)"/>
    </Target>

</Project>
