<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
        <RunWorkingDirectory>$(MSBuildProjectDirectory)</RunWorkingDirectory>
        <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Cake.Frosting" Version="2.2.0"/>
        <PackageReference Include="Cake.Git" Version="2.0.0"/>
        <PackageReference Include="Cake.Incubator" Version="7.0.0"/>
        <PackageReference Include="Cake.Json" Version="7.0.1"/>
        <PackageReference Include="Microsoft.Build" Version="17.2.0"/>
    </ItemGroup>
    <ItemGroup>
        <Content Include="$(SolutionDir)/.github/workflows/*.yml" LinkBase="ci"/>
    </ItemGroup>
</Project>
