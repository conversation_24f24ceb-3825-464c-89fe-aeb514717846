@echo off
title Sons of the Forest - Master Mod Launcher
color 0B
cls
echo.
echo ████████████████████████████████████████████
echo █                                          █
echo █    Sons of the Forest Mod Launcher      █
echo █                                          █
echo ████████████████████████████████████████████
echo.

echo [36mDetecting available mods...[0m
echo.

REM Check for ReiHook (BepInEx method)
set REIHOOK_AVAILABLE=0
if exist "BepInEx\plugins\ReiHook.dll" (
    echo [32m✓ ReiHook mod detected [BepInEx] - Menu Key: F4[0m
    set REIHOOK_AVAILABLE=1
) else if exist "ReiHook for Sons of the Forest v2.0_[unknowncheats.me]_.dll" (
    echo [33m! ReiHook file found but not installed[0m
    set REIHOOK_AVAILABLE=2
)

REM Check for SOTFmenu (Direct injection method)
set SOTFMENU_AVAILABLE=0
if exist "SOTFmenu.dll" (
    echo [33m! SOTFmenu detected [Direct Injection] - Menu Key: P[0m
    echo [37m  Note: ReiHook is more reliable if available[0m
    set SOTFMENU_AVAILABLE=1
)

REM Check BepInEx framework
set BEPINEX_AVAILABLE=0
if exist "BepInEx" (
    echo [32m✓ BepInEx framework installed[0m
    set BEPINEX_AVAILABLE=1
) else (
    echo [31m✗ BepInEx framework not installed[0m
)

echo.
echo [36mAvailable Options:[0m
echo.

if %REIHOOK_AVAILABLE%==1 (
    echo [32m[1] Launch with ReiHook mod [RECOMMENDED][0m
    echo [37m    • More stable BepInEx injection[0m
    echo [37m    • Menu Key: F4[0m
    echo [37m    • Better compatibility[0m
    echo.
)

if %REIHOOK_AVAILABLE%==2 (
    echo [33m[1] Install and launch ReiHook mod [RECOMMENDED][0m
    echo [37m    • Requires BepInEx framework[0m
    echo [37m    • More stable than SOTFmenu[0m
    echo.
)

if %SOTFMENU_AVAILABLE%==1 (
    echo [33m[2] Launch with SOTFmenu [BASIC][0m
    echo [37m    • Direct injection method[0m
    echo [37m    • Menu Key: P[0m
    echo [37m    • May have compatibility issues[0m
    echo.
)

echo [36m[3] Setup/Install mods[0m
echo [36m[4] View setup guides[0m
echo [36m[5] Launch game without mods[0m
echo [36m[6] Exit[0m
echo.

set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" (
    if %REIHOOK_AVAILABLE%==1 (
        echo.
        echo [36mLaunching with ReiHook mod...[0m
        call LAUNCH_REIHOOK.bat
    ) else if %REIHOOK_AVAILABLE%==2 (
        echo.
        echo [36mSetting up ReiHook mod...[0m
        if %BEPINEX_AVAILABLE%==1 (
            call INSTALL_REIHOOK.bat
        ) else (
            echo [31mBepInEx required for ReiHook![0m
            call INSTALL_BEPINEX.bat
        )
    ) else (
        echo [31mReiHook not available![0m
        echo [37mDownload ReiHook from UnknownCheats first.[0m
        pause
    )
) else if "%choice%"=="2" (
    if %SOTFMENU_AVAILABLE%==1 (
        echo.
        echo [33mLaunching with SOTFmenu...[0m
        echo [37mNote: ReiHook is more reliable if you have it.[0m
        call SIMPLE_LAUNCH.bat
    ) else (
        echo [31mSOTFmenu not available![0m
        pause
    )
) else if "%choice%"=="3" (
    echo.
    echo [36mSetup Options:[0m
    echo [37m[A] Install BepInEx framework[0m
    echo [37m[B] Install ReiHook mod[0m
    echo [37m[C] Setup SOTFmenu[0m
    echo.
    set /p setup="Choose setup option (A/B/C): "
    if /i "%setup%"=="A" call INSTALL_BEPINEX.bat
    if /i "%setup%"=="B" call INSTALL_REIHOOK.bat
    if /i "%setup%"=="C" call FINAL_SETUP.bat
) else if "%choice%"=="4" (
    echo.
    echo [36mOpening setup guides...[0m
    start notepad "REIHOOK_SETUP_GUIDE.txt"
    start notepad "INSTALLATION_SUCCESS.txt"
) else if "%choice%"=="5" (
    echo.
    echo [36mLaunching Sons of the Forest without mods...[0m
    start "" "SonsOfTheForest.exe"
    echo [32mGame launched![0m
) else if "%choice%"=="6" (
    echo.
    echo [37mGoodbye![0m
    exit /b 0
) else (
    echo.
    echo [31mInvalid choice. Please run again and select 1-6.[0m
    pause
    exit /b 1
)

echo.
pause
