{"names": ["UnityEngine.dll", "UnityEngine.AIModule.dll", "UnityEngine.ARModule.dll", "UnityEngine.AccessibilityModule.dll", "UnityEngine.AndroidJNIModule.dll", "UnityEngine.AnimationModule.dll", "UnityEngine.AssetBundleModule.dll", "UnityEngine.AudioModule.dll", "UnityEngine.ClothModule.dll", "UnityEngine.ClusterInputModule.dll", "UnityEngine.ClusterRendererModule.dll", "UnityEngine.ContentLoadModule.dll", "UnityEngine.CoreModule.dll", "UnityEngine.CrashReportingModule.dll", "UnityEngine.DSPGraphModule.dll", "UnityEngine.DirectorModule.dll", "UnityEngine.GIModule.dll", "UnityEngine.GameCenterModule.dll", "UnityEngine.GridModule.dll", "UnityEngine.HotReloadModule.dll", "UnityEngine.IMGUIModule.dll", "UnityEngine.ImageConversionModule.dll", "UnityEngine.InputModule.dll", "UnityEngine.InputLegacyModule.dll", "UnityEngine.JSONSerializeModule.dll", "UnityEngine.LocalizationModule.dll", "UnityEngine.NVIDIAModule.dll", "UnityEngine.ParticleSystemModule.dll", "UnityEngine.PerformanceReportingModule.dll", "UnityEngine.PhysicsModule.dll", "UnityEngine.Physics2DModule.dll", "UnityEngine.ProfilerModule.dll", "UnityEngine.PropertiesModule.dll", "UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "UnityEngine.ScreenCaptureModule.dll", "UnityEngine.SharedInternalsModule.dll", "UnityEngine.SpriteMaskModule.dll", "UnityEngine.SpriteShapeModule.dll", "UnityEngine.StreamingModule.dll", "UnityEngine.SubstanceModule.dll", "UnityEngine.SubsystemsModule.dll", "UnityEngine.TLSModule.dll", "UnityEngine.TerrainModule.dll", "UnityEngine.TerrainPhysicsModule.dll", "UnityEngine.TextCoreFontEngineModule.dll", "UnityEngine.TextCoreTextEngineModule.dll", "UnityEngine.TextRenderingModule.dll", "UnityEngine.TilemapModule.dll", "UnityEngine.UIModule.dll", "UnityEngine.UIElementsModule.dll", "UnityEngine.UmbraModule.dll", "UnityEngine.UnityAnalyticsModule.dll", "UnityEngine.UnityAnalyticsCommonModule.dll", "UnityEngine.UnityConnectModule.dll", "UnityEngine.UnityCurlModule.dll", "UnityEngine.UnityTestProtocolModule.dll", "UnityEngine.UnityWebRequestModule.dll", "UnityEngine.UnityWebRequestAssetBundleModule.dll", "UnityEngine.UnityWebRequestAudioModule.dll", "UnityEngine.UnityWebRequestTextureModule.dll", "UnityEngine.UnityWebRequestWWWModule.dll", "UnityEngine.VFXModule.dll", "UnityEngine.VRModule.dll", "UnityEngine.VehiclesModule.dll", "UnityEngine.VideoModule.dll", "UnityEngine.VirtualTexturingModule.dll", "UnityEngine.WindModule.dll", "UnityEngine.XRModule.dll", "Assembly-CSharp.dll", "com.rlabrecque.steamworks.net.dll", "Sons.Trees.dll", "Sons.VFX.dll", "Sons.Gameplay.LevelProps.dll", "Sons.Ai.Utilities.dll", "NWH.Common.SceneManagement.dll", "PathologicalGames.dll", "com.alteregogames.aeg-fsr.Runtime.dll", "DynamicBone.dll", "NWH.Common.NUI.dll", "Endnight.Utilities.Dedicated.dll", "Unity.ScriptableBuildPipeline.dll", "Sons.Animation.Mae.dll", "Sons.FMOD.dll", "Sons.FpsUtility.dll", "Drawing.dll", "TheForest.Utils.dll", "AdvancedTerrainGrass.dll", "NWH.Common.FloatingOrigin.dll", "NWH.Common.Cameras.dll", "NWH.Common.Demo.dll", "AmplifyImposters.dll", "Unity.RenderPipelines.HighDefinition.Config.Runtime.dll", "Sons.Events.dll", "Endnight.Utilities.Misc.dll", "Unity.RenderPipelines.Core.ShaderLibrary.dll", "Sons.Gui.Multiplayer.dll", "Tayx.Graphy.dll", "Unity.Collections.dll", "StreamingAssetsManager.dll", "Sons.UiElements.dll", "Endnight.Utilities.CommandLine.dll", "Sons.Testing.dll", "Unity.ProBuilder.Poly2Tri.dll", "Sons.ConstructionBoltToken.dll", "Sons.Quality.dll", "Endnight.Localization.dll", "Sons.Save.dll", "Sons.Tests.dll", "AstarPathfindingProject.dll", "Unity.TextMeshPro.dll", "Sons.Gui.dll", "Obi.dll", "Sons.Multiplayer.dll", "Endnight.PrefabStore.dll", "Sons.Settings.dll", "Sons.Attributes.dll", "Sons.Atmosphere.dll", "Unity.MemoryProfiler.dll", "Sons.Weapon.dll", "Sons.Lodding.dll", "Sons.Subtitles.Tooling.dll", "Unity.Burst.dll", "Sons.Gui.Options.dll", "Multiplayer.Dedicated.dll", "NWH.Common.Vehicle.dll", "Sons.Hardware.dll", "Unity.ProBuilder.dll", "Sons.Input.dll", "Sons.Gui.Colors.dll", "FMOD.Unity.dll", "Sons.Scratchpad.dll", "ShapesSamples.dll", "Sons.Gameplay.GameSetup.dll", "Sons.VFX.SimpleUnderwater.dll", "NWH.Common.Input.dll", "Sons.Cinematics.dll", "AnimatorEvent.dll", "MicroSplatChanges.dll", "Sons.ObsoleteMonos.dll", "Sons.SonsTerrain.dll", "Sons.Utilities.dll", "Sons.Loading.dll", "OSK.dll", "Sons.Environment.dll", "com.alteregogames.aeg-fsr.Runtime.HDRP.dll", "Sons.Rendering.dll", "SonsTerrainScripts.dll", "Sons.Ai.Vail.dll", "Unity.Splines.dll", "Sons.Gui.Multiplayer.Notifications.dll", "Sons.Dlss.dll", "Crest.dll", "Endnight.Utilities.Logging.dll", "Unity.Addressables.dll", "TheForest.Modding.dll", "PathCreator.dll", "BlobNetworking.dll", "Unity.VisualEffectGraph.Runtime.dll", "Unity.RenderPipelines.Core.Runtime.dll", "Sons.Multiplayer.NamedIntIds.dll", "Unity.ProBuilder.Stl.dll", "Unity.ResourceManager.dll", "Crest.Examples.dll", "Sons.Rocks.dll", "Endnight.dll", "Sons.FieldOfView.dll", "Sons.dll", "NatureManufacture.SplineSystem.dll", "Unity.RenderPipelines.BatchRendererGroup-Testing.dll", "Sons.Crest.dll", "Unity.InternalAPIEngineBridge.013.dll", "Unity.ProBuilder.Csg.dll", "Unity.Formats.Fbx.Runtime.dll", "Sons.Extensions.dll", "ShapesRuntime.dll", "Sons.SettingsWrappers.dll", "Sons.StatSystem.dll", "NWH.Common.Utility.dll", "Sons.Subtitles.dll", "Sons.GameApplication.dll", "Sons.Gui.VersionUpgrade.dll", "Sons.Debug.dll", "NWH.DWP2.dll", "Sons.PostProcessing.FoundFootage.dll", "Sons.Interfaces.dll", "VFXGraph.OutputEventHandlers.dll", "NWH.Common.CoM.dll", "Sons.SteamTimelineExtended.dll", "Sons.Physics.dll", "FbxBuildTestAssets.dll", "Sons.Gameplay.StatusEffects.dll", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "Endnight.Backtrace.dll", "PackageTools.dll", "BulletBallistics.dll", "Sons.Geometry.dll", "Unity.TerrainTools.dll", "Unity.Localization.dll", "Sons.Construction.dll", "Sons.PostProcessing.dll", "Bolt.Unity.dll", "UnityEngine.UI.dll", "Sons.Construction.Utils.dll", "Sons.Ragdoll.dll", "VolumetricBloodFX.dll", "RenderStreamDepth.dll", "Endnight.Utilities.Configurations.dll", "Sons.Electricity.dll", "DigitalRuby.ProceduralLightning.dll", "Unity.Timeline.dll", "Sons.Areas.dll", "Unity.InputSystem.dll", "Backtrace.Unity.dll", "Upgen.Lighting.dll", "Unity.CrossSceneReference.dll", "Autodesk.Fbx.dll", "Sons.Item.dll", "Sons.Multiplayer.Configurations.dll", "Unity.ProBuilder.KdTree.dll", "Unity.Mathematics.dll", "NWH.Common.AssetInfo.dll", "RootMotion.dll", "JBooth.MicroSplat.Core.dll", "Unity.RenderPipelines.HighDefinition.Runtime.dll", "Locomotion.dll", "Pathfinding.Poly2Tri.dll", "Unity.Collections.LowLevel.ILSupport.dll", "udpkit.common.dll", "udpkit.dll", "bolt.dll", "Sirenix.OdinInspector.Attributes.dll", "Pathfinding.ClipperLib.dll", "Unity.Burst.Unsafe.dll", "bolt.user.dll", "Newtonsoft.Json.dll", "CommandLine.dll", "TheForest.Commons.dll", "Pathfinding.Ionic.Zip.Reduced.dll"], "types": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16]}