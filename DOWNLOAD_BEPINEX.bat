@echo off
title BepInEx Download Helper
color 0E
echo.
echo ========================================
echo   BepInEx Download Helper
echo ========================================
echo.

echo [33m⚠️  IMPORTANT: You need the RELEASES page, not the main repo![0m
echo.
echo [36mCorrect Download Steps:[0m
echo.
echo [37m1. I'll open the RELEASES page (not the main repository)[0m
echo [37m2. Look for the latest version (6.0.0+)[0m
echo [37m3. Download: "BepInEx_UnityIL2CPP_x64_6.0.0-be.XXX.zip"[0m
echo [37m4. Make sure it says "UnityIL2CPP_x64" in the filename[0m
echo.

echo [31mDO NOT DOWNLOAD:[0m
echo [37m• BepInEx_Unity_x64 (wrong version)[0m
echo [37m• BepInEx_UnityMono_x64 (wrong version)[0m
echo [37m• Source code files[0m
echo.

echo [32mDO DOWNLOAD:[0m
echo [37m• BepInEx_UnityIL2CPP_x64_6.0.0-be.XXX.zip[0m
echo.

set /p open="Open the correct BepInEx releases page? (y/n): "
if /i "%open%"=="y" (
    echo.
    echo [36mOpening BepInEx releases page...[0m
    start "" "https://github.com/BepInEx/BepInEx/releases"
    echo [32m✓ Releases page opened in browser[0m
    echo.
    echo [33mLook for the file that contains "UnityIL2CPP_x64" in the name![0m
) else (
    echo [37mYou can manually go to: https://github.com/BepInEx/BepInEx/releases[0m
)

echo.
echo [36mAfter downloading:[0m
echo [37m1. Extract the ZIP file[0m
echo [37m2. Copy ALL contents to your game directory[0m
echo [37m3. Run the game once to initialize BepInEx[0m
echo [37m4. Then run INSTALL_REIHOOK.bat[0m
echo.

echo [33mFiles you should see after extraction:[0m
echo [37m• BepInEx\ (folder)[0m
echo [37m• winhttp.dll (file)[0m
echo [37m• doorstop_config.ini (file)[0m
echo.

set /p extracted="Have you downloaded and extracted BepInEx? (y/n): "
if /i "%extracted%"=="y" (
    echo.
    echo [36mChecking BepInEx installation...[0m
    
    if exist "BepInEx" (
        echo [32m✓ BepInEx folder found[0m
    ) else (
        echo [31m✗ BepInEx folder not found[0m
        echo [37m  Make sure you extracted to the game directory[0m
    )
    
    if exist "winhttp.dll" (
        echo [32m✓ winhttp.dll found[0m
    ) else (
        echo [31m✗ winhttp.dll not found[0m
        echo [37m  This file is required for BepInEx to work[0m
    )
    
    if exist "BepInEx" AND exist "winhttp.dll" (
        echo.
        echo [32m🎉 BepInEx appears to be installed correctly![0m
        echo.
        echo [36mNext steps:[0m
        echo [37m1. Run Sons of the Forest once to initialize BepInEx[0m
        echo [37m2. Close the game after it loads[0m
        echo [37m3. Run INSTALL_REIHOOK.bat to install the mod[0m
        echo.
        
        set /p initialize="Run the game now to initialize BepInEx? (y/n): "
        if /i "%initialize%"=="y" (
            echo [36mLaunching game for BepInEx initialization...[0m
            start "" "SonsOfTheForest.exe"
            echo.
            echo [33mLet the game load completely, then close it.[0m
            echo [33mAfter that, run INSTALL_REIHOOK.bat[0m
        )
    ) else (
        echo.
        echo [31mBepInEx installation incomplete.[0m
        echo [37mPlease extract all files to the game directory.[0m
    )
) else (
    echo.
    echo [37mDownload and extract BepInEx first, then run this script again.[0m
)

echo.
pause
