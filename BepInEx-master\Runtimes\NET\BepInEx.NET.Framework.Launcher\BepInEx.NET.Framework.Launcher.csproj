﻿<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <Description>BepInEx support library for .NET Framework games</Description>
        <OutputType>Exe</OutputType>
        <TargetFrameworks>net40;net452</TargetFrameworks>
        <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
        <AppendTargetFrameworkToOutputPath>true</AppendTargetFrameworkToOutputPath>
        <PlatformTarget>x86</PlatformTarget>
        <OutputPath>$(BuildDir)/NET.Framework</OutputPath>
    </PropertyGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\..\BepInEx.Core\BepInEx.Core.csproj"/>
        <ProjectReference Include="..\BepInEx.NET.Common\BepInEx.NET.Common.csproj"/>
        <ProjectReference Include="..\..\..\BepInEx.Preloader.Core\BepInEx.Preloader.Core.csproj" PrivateAssets="All"/>
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="HarmonyX" Version="2.10.2" />
        <PackageReference Include="Microsoft.NETFramework.ReferenceAssemblies" Version="1.0.3" PrivateAssets="All"/>
    </ItemGroup>
    <Import Project="..\BepInEx.NET.Shared\BepInEx.NET.Shared.projitems" Label="Shared"/>
</Project>
