@echo off
title SOTFmenu Troubleshooting
color 0E
echo.
echo ========================================
echo   SOTFmenu Troubleshooting Assistant
echo ========================================
echo.

echo Checking mod installation...
echo.

if not exist "SOTFmenu.dll" (
    echo [31mERROR: SOTFmenu.dll not found![0m
    echo The mod file is missing from the game directory.
    pause
    exit /b 1
) else (
    echo [32m✓ SOTFmenu.dll found[0m
    for %%A in ("SOTFmenu.dll") do echo   Size: %%~zA bytes
    for %%A in ("SOTFmenu.dll") do echo   Date: %%~tA
)

echo.
echo Checking configuration...
if exist "%USERPROFILE%\Documents\SOTF\config.ini" (
    echo [32m✓ Configuration file exists[0m
) else (
    echo [33m! Configuration file missing - creating now...[0m
    if not exist "%USERPROFILE%\Documents\SOTF" mkdir "%USERPROFILE%\Documents\SOTF"
    echo [Settings] > "%USERPROFILE%\Documents\SOTF\config.ini"
    echo GamePath=%CD%\SonsOfTheForest.exe >> "%USERPROFILE%\Documents\SOTF\config.ini"
    echo OpenCloseKey=P >> "%USERPROFILE%\Documents\SOTF\config.ini"
    echo ShowTerminal=false >> "%USERPROFILE%\Documents\SOTF\config.ini"
    echo [32m✓ Configuration created[0m
)

echo.
echo ========================================
echo   COMMON SOLUTIONS FOR MENU NOT OPENING:
echo ========================================
echo.

echo [36m1. GAME DISPLAY MODE (MOST IMPORTANT):[0m
echo [37m   • Game MUST be in Borderless Window mode[0m
echo [37m   • Fullscreen mode will NOT work[0m
echo [37m   • Go to Settings → Graphics → Display Mode[0m
echo.

echo [36m2. OVERLAY PROGRAMS:[0m
echo [37m   • Close MSI Afterburner[0m
echo [37m   • Close RTSS (RivaTuner Statistics Server)[0m
echo [37m   • Close Discord overlay[0m
echo [37m   • Close Steam overlay[0m
echo [37m   • Close any recording software[0m
echo.

echo [36m3. ANTIVIRUS BLOCKING:[0m
echo [37m   • Check if antivirus quarantined SOTFmenu.dll[0m
echo [37m   • Add exception for the game folder[0m
echo [37m   • Temporarily disable real-time protection[0m
echo.

echo [36m4. ADMINISTRATOR RIGHTS:[0m
echo [37m   • Try running the game as administrator[0m
echo [37m   • Right-click game launcher → Run as administrator[0m
echo.

echo [36m5. ALTERNATIVE KEYS TO TRY:[0m
echo [37m   • Try pressing INSERT key instead of P[0m
echo [37m   • Try pressing HOME key[0m
echo [37m   • Try pressing F1 key[0m
echo.

echo [36m6. INJECTION METHOD:[0m
echo [37m   • The mod needs to inject into the game process[0m
echo [37m   • Make sure you're launching through our launcher[0m
echo [37m   • Don't launch directly from Steam[0m
echo.

echo ========================================
echo   TESTING STEPS:
echo ========================================
echo.

echo [33mTry these steps in order:[0m
echo.
echo [37m1. Close the game completely[0m
echo [37m2. Close all overlay programs[0m
echo [37m3. Run this launcher as administrator[0m
echo [37m4. Launch game through our SIMPLE_LAUNCH.bat[0m
echo [37m5. Set game to Borderless Window mode[0m
echo [37m6. Wait for game to fully load[0m
echo [37m7. Try pressing P, INSERT, HOME, or F1[0m
echo.

set /p test="Would you like to launch the game now for testing? (y/n): "
if /i "%test%"=="y" (
    echo.
    echo [36mLaunching game for testing...[0m
    echo [33mRemember: Set to Borderless Window mode first![0m
    echo.
    start "" "SonsOfTheForest.exe"
    echo [32mGame launched. Try the menu keys once it loads.[0m
) else (
    echo [37mTest cancelled. Run SIMPLE_LAUNCH.bat when ready.[0m
)

echo.
pause
