@echo off
title SOTFmenu Diagnostic Tool
color 0C
echo.
echo ========================================
echo   SOTFmenu Diagnostic Tool
echo ========================================
echo.

echo Running comprehensive diagnostics...
echo.

REM Check if game is running
tasklist /fi "imagename eq SonsOfTheForest.exe" 2>NUL | find /i /n "SonsOfTheForest.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [33m! Sons of the Forest is currently running[0m
    echo [37m  Please close the game before running diagnostics[0m
    set /p close="Close the game now and press Enter to continue..."
) else (
    echo [32m✓ Game is not running - good for diagnostics[0m
)

echo.
echo [36m=== FILE CHECKS ===[0m

if exist "SOTFmenu.dll" (
    echo [32m✓ SOTFmenu.dll found[0m
    for %%A in ("SOTFmenu.dll") do (
        echo [37m  Size: %%~zA bytes[0m
        echo [37m  Date: %%~tA[0m
    )
    
    REM Check if file is blocked
    powershell -Command "Get-Item 'SOTFmenu.dll' | Get-ItemProperty -Name * | Select-Object PSPath" >nul 2>&1
    if errorlevel 1 (
        echo [31m  ⚠️  File may be blocked by Windows[0m
    ) else (
        echo [32m  ✓ File is accessible[0m
    )
) else (
    echo [31m✗ SOTFmenu.dll NOT FOUND[0m
    echo [37m  The mod file is missing![0m
)

echo.
echo [36m=== CONFIGURATION CHECKS ===[0m

if exist "%USERPROFILE%\Documents\SOTF" (
    echo [32m✓ SOTF folder exists[0m
    if exist "%USERPROFILE%\Documents\SOTF\config.ini" (
        echo [32m✓ Config file exists[0m
        echo [37m  Contents:[0m
        type "%USERPROFILE%\Documents\SOTF\config.ini" | findstr /n ".*"
    ) else (
        echo [31m✗ Config file missing[0m
    )
) else (
    echo [31m✗ SOTF folder missing[0m
)

echo.
echo [36m=== SYSTEM CHECKS ===[0m

REM Check for admin rights
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [32m✓ Running as Administrator[0m
) else (
    echo [31m✗ NOT running as Administrator[0m
    echo [37m  This is likely the problem![0m
)

REM Check for common blocking processes
echo.
echo [36m=== OVERLAY PROGRAM CHECKS ===[0m

tasklist /fi "imagename eq MSIAfterburner.exe" 2>NUL | find /i /n "MSIAfterburner.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [31m✗ MSI Afterburner is running (BLOCKS MOD)[0m
) else (
    echo [32m✓ MSI Afterburner not detected[0m
)

tasklist /fi "imagename eq RTSS.exe" 2>NUL | find /i /n "RTSS.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [31m✗ RTSS is running (BLOCKS MOD)[0m
) else (
    echo [32m✓ RTSS not detected[0m
)

tasklist /fi "imagename eq Discord.exe" 2>NUL | find /i /n "Discord.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [33m! Discord is running (may interfere)[0m
) else (
    echo [32m✓ Discord not detected[0m
)

echo.
echo [36m=== ANTIVIRUS CHECKS ===[0m

REM Check Windows Defender real-time protection
powershell -Command "Get-MpPreference | Select-Object DisableRealtimeMonitoring" 2>nul | findstr "False" >nul
if %errorlevel% == 0 (
    echo [33m! Windows Defender Real-time Protection is ON[0m
    echo [37m  This may block the mod injection[0m
) else (
    echo [32m✓ Windows Defender Real-time Protection appears disabled[0m
)

echo.
echo [36m=== RECOMMENDATIONS ===[0m
echo.

echo [33mBased on diagnostics, try these solutions:[0m
echo.

net session >nul 2>&1
if not %errorLevel% == 0 (
    echo [31m1. RUN AS ADMINISTRATOR (CRITICAL)[0m
    echo [37m   Right-click any launcher → Run as administrator[0m
    echo.
)

tasklist /fi "imagename eq MSIAfterburner.exe" 2>NUL | find /i /n "MSIAfterburner.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [31m2. CLOSE MSI AFTERBURNER (CRITICAL)[0m
    echo [37m   This program blocks DLL injection[0m
    echo.
)

tasklist /fi "imagename eq RTSS.exe" 2>NUL | find /i /n "RTSS.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [31m3. CLOSE RTSS (CRITICAL)[0m
    echo [37m   This program blocks DLL injection[0m
    echo.
)

echo [36m4. TRY MANUAL INJECTION METHOD[0m
echo [37m   The automatic injection may not be working[0m
echo [37m   We can try a different approach[0m
echo.

echo [36m5. VERIFY GAME VERSION COMPATIBILITY[0m
echo [37m   The mod may need updating for your game version[0m
echo.

set /p next="Would you like to try the manual injection method? (y/n): "
if /i "%next%"=="y" (
    echo.
    echo [36mPreparing manual injection method...[0m
    call :manual_injection
) else (
    echo.
    echo [37mDiagnostics complete. Address the issues above and try again.[0m
)

echo.
pause
exit /b 0

:manual_injection
echo.
echo [36m=== MANUAL INJECTION METHOD ===[0m
echo.
echo [37mThis method will:[0m
echo [37m1. Start the game normally[0m
echo [37m2. Wait for it to fully load[0m
echo [37m3. Attempt manual DLL injection[0m
echo.

set /p proceed="Proceed with manual injection? (y/n): "
if /i not "%proceed%"=="y" exit /b 0

echo.
echo [36mStarting game...[0m
start "" "SonsOfTheForest.exe"

echo [37mWaiting for game to start (30 seconds)...[0m
timeout /t 30 /nobreak

echo.
echo [36mAttempting manual injection...[0m
echo [37mNote: This is a basic attempt. Advanced injection tools may be needed.[0m

REM Try to find the process ID
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq SonsOfTheForest.exe" /fo csv ^| find "SonsOfTheForest.exe"') do set PID=%%i

if defined PID (
    echo [32m✓ Game process found (PID: %PID%)[0m
    echo [37m  The mod should inject automatically when the process is detected[0m
    echo [37m  Try the menu keys now: P, INSERT, HOME, F1[0m
) else (
    echo [31m✗ Could not find game process[0m
    echo [37m  Make sure the game is running[0m
)

exit /b 0
